// 详情页面JavaScript

// DOM元素
const videoPlayer = document.getElementById('mainVideoPlayer');
const playerOverlay = document.getElementById('playerOverlay');
const playButton = document.getElementById('playButton');
const videoTitle = document.getElementById('videoTitle');
const videoViews = document.getElementById('videoViews');
const videoDuration = document.getElementById('videoDuration');
const videoDate = document.getElementById('videoDate');
const videoCategory = document.getElementById('videoCategory');
const videoDescription = document.getElementById('videoDescription');
const videoTags = document.getElementById('videoTags');
const likeBtn = document.getElementById('likeBtn');
const likeCount = document.getElementById('likeCount');
const favoriteBtn = document.getElementById('favoriteBtn');
const shareBtn = document.getElementById('shareBtn');
const downloadBtn = document.getElementById('downloadBtn');
const expandBtn = document.getElementById('expandBtn');
const currentTitle = document.getElementById('currentTitle');
const categoryLink = document.getElementById('categoryLink');
const relatedGrid = document.getElementById('relatedGrid');
const commentsList = document.getElementById('commentsList');
const commentCount = document.getElementById('commentCount');
const commentText = document.getElementById('commentText');
const submitComment = document.getElementById('submitComment');
const loadMoreComments = document.getElementById('loadMoreComments');
const shareModal = document.getElementById('shareModal');
const closeShareModal = document.getElementById('closeShareModal');
const shareUrl = document.getElementById('shareUrl');
const copyUrlBtn = document.getElementById('copyUrlBtn');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const navToggle = document.getElementById('navToggle');

// 状态变量
let currentVideo = null;
let isLiked = false;
let isFavorited = false;
let isDescriptionExpanded = false;
let watchStartTime = 0;
let lastProgressUpdate = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
});

// 初始化页面
function initializePage() {
    // 从URL参数获取视频ID
    const urlParams = new URLSearchParams(window.location.search);
    const videoId = parseInt(urlParams.get('id'));
    
    if (videoId) {
        loadVideoDetails(videoId);
    } else {
        // 如果没有ID，跳转到首页
        window.location.href = 'index.html';
    }
}

// 加载视频详情
function loadVideoDetails(videoId) {
    currentVideo = videoData.find(video => video.id === videoId);
    
    if (!currentVideo) {
        // 视频不存在，跳转到首页
        window.location.href = 'index.html';
        return;
    }
    
    // 更新页面内容
    updateVideoInfo();
    updateBreadcrumb();
    loadRelatedVideos();
    loadComments();
    
    // 设置分享URL
    shareUrl.value = window.location.href;
}

// 更新视频信息
function updateVideoInfo() {
    // 基本信息
    videoTitle.textContent = currentVideo.title;
    videoViews.textContent = currentVideo.views;
    videoDuration.textContent = currentVideo.duration;
    videoDate.textContent = utils.getRelativeTime(currentVideo.publishDate);
    videoCategory.textContent = utils.getCategoryName(currentVideo.category);
    
    // 描述
    videoDescription.innerHTML = `<p>${currentVideo.description}</p>`;
    
    // 标签
    videoTags.innerHTML = '';
    currentVideo.tags.forEach(tag => {
        const tagElement = document.createElement('span');
        tagElement.className = 'tag';
        tagElement.textContent = tag;
        tagElement.addEventListener('click', () => {
            window.location.href = `search.html?q=${encodeURIComponent(tag)}`;
        });
        videoTags.appendChild(tagElement);
    });
    
    // 点赞数
    likeCount.textContent = utils.formatNumber(currentVideo.likes);

    // 检查收藏状态
    if (window.authManager && window.authManager.isLoggedIn()) {
        isFavorited = window.authManager.isFavorited(currentVideo.id);
        updateFavoriteButton();
    }

    // 视频播放器
    videoPlayer.src = currentVideo.videoUrl;
    videoPlayer.poster = currentVideo.thumbnail;

    // 显示播放覆盖层
    showPlayerOverlay();
}

// 更新面包屑导航
function updateBreadcrumb() {
    currentTitle.textContent = currentVideo.title;
    categoryLink.textContent = utils.getCategoryName(currentVideo.category);
    categoryLink.href = `list.html?category=${currentVideo.category}`;
}

// 加载相关视频
function loadRelatedVideos() {
    const relatedVideos = utils.getRelatedVideos(currentVideo.id, 6);
    
    relatedGrid.innerHTML = '';
    relatedVideos.forEach(video => {
        const cardElement = document.createElement('div');
        cardElement.innerHTML = utils.createVideoCard(video, false);
        const card = cardElement.firstElementChild;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            window.location.href = `detail.html?id=${video.id}`;
        });
        
        relatedGrid.appendChild(card);
    });
}

// 加载评论
function loadComments() {
    const comments = window.commentsData ? window.commentsData[currentVideo.id] || [] : [];
    commentCount.textContent = comments.length;
    
    commentsList.innerHTML = '';
    comments.forEach(comment => {
        const commentElement = createCommentElement(comment);
        commentsList.appendChild(commentElement);
    });
    
    // 如果没有评论，隐藏加载更多按钮
    if (comments.length === 0) {
        loadMoreComments.style.display = 'none';
    }
}

// 创建评论元素
function createCommentElement(comment) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'comment-item';
    commentDiv.innerHTML = `
        <div class="comment-header">
            <div class="comment-avatar">${comment.avatar}</div>
            <div class="comment-info">
                <div class="comment-author">${comment.author}</div>
                <div class="comment-time">${comment.time}</div>
            </div>
        </div>
        <div class="comment-content">${comment.content}</div>
        <div class="comment-actions-bar">
            <button class="comment-action like-comment" data-comment-id="${comment.id}">
                <i class="fas fa-thumbs-up"></i> ${comment.likes}
            </button>
            <button class="comment-action reply-comment" data-comment-id="${comment.id}">
                <i class="fas fa-reply"></i> 回复
            </button>
        </div>
    `;
    return commentDiv;
}

// 显示播放器覆盖层
function showPlayerOverlay() {
    playerOverlay.classList.add('show');
}

// 隐藏播放器覆盖层
function hidePlayerOverlay() {
    playerOverlay.classList.remove('show');
}

// 绑定事件
function bindEvents() {
    // 视频播放
    playButton.addEventListener('click', () => {
        videoPlayer.play();
        hidePlayerOverlay();
        recordPlayStart();
    });

    videoPlayer.addEventListener('play', () => {
        hidePlayerOverlay();
        recordPlayStart();
    });
    videoPlayer.addEventListener('pause', showPlayerOverlay);
    videoPlayer.addEventListener('ended', () => {
        showPlayerOverlay();
        recordPlayProgress(100);
    });

    // 定期更新播放进度
    videoPlayer.addEventListener('timeupdate', updatePlayProgress);
    
    // 点击视频区域播放/暂停
    videoPlayer.addEventListener('click', () => {
        if (videoPlayer.paused) {
            videoPlayer.play();
        } else {
            videoPlayer.pause();
        }
    });
    
    // 动作按钮
    likeBtn.addEventListener('click', toggleLike);
    favoriteBtn.addEventListener('click', toggleFavorite);
    shareBtn.addEventListener('click', openShareModal);
    downloadBtn.addEventListener('click', downloadVideo);
    
    // 描述展开/收起
    expandBtn.addEventListener('click', toggleDescription);
    
    // 评论提交
    submitComment.addEventListener('click', submitNewComment);
    
    // 分享模态框
    closeShareModal.addEventListener('click', closeShareModalHandler);
    shareModal.addEventListener('click', (e) => {
        if (e.target === shareModal) {
            closeShareModalHandler();
        }
    });
    
    // 复制链接
    copyUrlBtn.addEventListener('click', copyShareUrl);
    
    // 分享选项
    document.querySelectorAll('.share-option').forEach(option => {
        option.addEventListener('click', (e) => {
            const platform = e.currentTarget.dataset.platform;
            shareToplatform(platform);
        });
    });
    
    // 搜索
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 移动端导航
    navToggle.addEventListener('click', () => {
        const navMenu = document.querySelector('.nav-menu');
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && shareModal.classList.contains('show')) {
            closeShareModalHandler();
        }
    });
}

// 切换点赞状态
function toggleLike() {
    isLiked = !isLiked;
    likeBtn.classList.toggle('active', isLiked);
    
    // 更新点赞数
    const currentLikes = parseInt(likeCount.textContent.replace(/[^\d]/g, ''));
    const newLikes = isLiked ? currentLikes + 1 : currentLikes - 1;
    likeCount.textContent = utils.formatNumber(newLikes);
    
    // 动画效果
    likeBtn.style.transform = 'scale(1.2)';
    setTimeout(() => {
        likeBtn.style.transform = 'scale(1)';
    }, 200);
}

// 切换收藏状态
function toggleFavorite() {
    if (!window.authManager || !window.authManager.isLoggedIn()) {
        showToast('请先登录', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.href);
        }, 1000);
        return;
    }

    if (isFavorited) {
        // 取消收藏
        const success = window.authManager.removeFromFavorites(currentVideo.id);
        if (success) {
            isFavorited = false;
            showToast('已取消收藏', 'success');
        }
    } else {
        // 添加收藏
        const success = window.authManager.addToFavorites(currentVideo);
        if (success) {
            isFavorited = true;
            showToast('已添加到收藏', 'success');
        } else {
            showToast('该视频已在收藏列表中', 'info');
        }
    }

    updateFavoriteButton();

    // 动画效果
    favoriteBtn.style.transform = 'scale(1.2)';
    setTimeout(() => {
        favoriteBtn.style.transform = 'scale(1)';
    }, 200);
}

// 更新收藏按钮状态
function updateFavoriteButton() {
    favoriteBtn.classList.toggle('active', isFavorited);
    const icon = favoriteBtn.querySelector('i');
    icon.className = isFavorited ? 'fas fa-heart' : 'far fa-heart';
    const span = favoriteBtn.querySelector('span');
    span.textContent = isFavorited ? '已收藏' : '收藏';
}

// 切换描述展开状态
function toggleDescription() {
    isDescriptionExpanded = !isDescriptionExpanded;
    const content = videoDescription.querySelector('.description-content') || videoDescription;
    
    content.classList.toggle('expanded', isDescriptionExpanded);
    expandBtn.textContent = isDescriptionExpanded ? '收起' : '展开全部';
}

// 提交新评论
function submitNewComment() {
    const content = commentText.value.trim();
    if (!content) {
        alert('请输入评论内容');
        return;
    }
    
    // 创建新评论
    const newComment = {
        id: Date.now(),
        author: '匿名用户',
        avatar: '匿',
        content: content,
        time: '刚刚',
        likes: 0
    };
    
    // 添加到评论列表
    const commentElement = createCommentElement(newComment);
    commentsList.insertBefore(commentElement, commentsList.firstChild);
    
    // 更新评论数
    const currentCount = parseInt(commentCount.textContent);
    commentCount.textContent = currentCount + 1;
    
    // 清空输入框
    commentText.value = '';
    
    // 显示成功提示
    alert('评论发表成功！');
}

// 打开分享模态框
function openShareModal() {
    shareModal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

// 关闭分享模态框
function closeShareModalHandler() {
    shareModal.classList.remove('show');
    document.body.style.overflow = 'auto';
}

// 复制分享链接
function copyShareUrl() {
    shareUrl.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const originalText = copyUrlBtn.textContent;
    copyUrlBtn.textContent = '已复制';
    setTimeout(() => {
        copyUrlBtn.textContent = originalText;
    }, 2000);
}

// 分享到平台
function shareToplatform(platform) {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(currentVideo.title);
    
    let shareUrl = '';
    
    switch (platform) {
        case 'wechat':
            // 微信分享通常需要特殊处理
            alert('请复制链接手动分享到微信');
            break;
        case 'weibo':
            shareUrl = `https://service.weibo.com/share/share.php?url=${url}&title=${title}`;
            break;
        case 'qq':
            shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}`;
            break;
        case 'link':
            copyShareUrl();
            return;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank');
    }
    
    closeShareModalHandler();
}

// 下载视频
function downloadVideo() {
    // 实际项目中这里应该处理视频下载逻辑
    alert('下载功能暂未开放');
}

// 执行搜索
function performSearch() {
    const keyword = searchInput.value.trim();
    if (keyword) {
        window.location.href = `search.html?q=${encodeURIComponent(keyword)}`;
    }
}

// 记录播放开始
function recordPlayStart() {
    watchStartTime = Date.now();
    lastProgressUpdate = 0;

    // 添加到播放历史（初始进度为0）
    if (window.authManager && window.authManager.isLoggedIn()) {
        window.authManager.addToHistory(currentVideo, 0);
    }
}

// 更新播放进度
function updatePlayProgress() {
    if (!videoPlayer.duration || !watchStartTime) return;

    const currentTime = videoPlayer.currentTime;
    const duration = videoPlayer.duration;
    const progress = Math.round((currentTime / duration) * 100);

    // 每5%进度更新一次历史记录
    if (progress - lastProgressUpdate >= 5) {
        recordPlayProgress(progress);
        lastProgressUpdate = progress;
    }
}

// 记录播放进度
function recordPlayProgress(progress) {
    if (window.authManager && window.authManager.isLoggedIn()) {
        window.authManager.addToHistory(currentVideo, progress);
    }
}
