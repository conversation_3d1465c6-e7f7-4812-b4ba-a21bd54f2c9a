# 短剧视频网站

一个现代化的响应式短剧视频网站，使用纯HTML、CSS和JavaScript开发，完美兼容PC端和移动端。

## 功能特点

### 🎬 核心功能
- **完整用户系统**: 注册、登录、个人中心、资料管理
- **多页面架构**: 首页、列表页、详情页、搜索页、用户中心完整体系
- **视频播放**: 支持在线视频播放，带有完整的播放控制
- **播放历史**: 自动记录观看进度，支持断点续播
- **个人收藏**: 收藏喜爱的视频，便于管理和回看
- **分类筛选**: 按言情、喜剧、剧情、动作、悬疑等分类浏览
- **高级搜索**: 支持关键词、分类、时长、发布时间等多维度搜索
- **视频详情**: 完整的视频信息、评论系统、相关推荐
- **响应式设计**: 完美适配PC、平板和手机设备

### 🎨 界面设计
- **现代化UI**: 采用渐变色彩和卡片式设计
- **轮播图**: 首页精美的轮播图展示热门内容
- **动画效果**: 流畅的过渡动画和悬停效果
- **移动端优化**: 专门优化的移动端导航和布局

### 📱 响应式特性
- **断点设计**: 768px和480px断点适配不同设备
- **弹性布局**: 使用CSS Grid和Flexbox实现灵活布局
- **触摸友好**: 移动端优化的按钮和交互区域
- **性能优化**: 图片懒加载和平滑滚动

## 项目结构

```
短剧视频网站/
├── index.html          # 主页面
├── list.html           # 分类列表页面
├── detail.html         # 视频详情页面
├── search.html         # 搜索结果页面
├── login.html          # 用户登录页面
├── register.html       # 用户注册页面
├── profile.html        # 用户中心页面
├── css/
│   ├── style.css       # 主样式文件
│   ├── list.css        # 列表页面样式
│   ├── detail.css      # 详情页面样式
│   ├── search.css      # 搜索页面样式
│   ├── auth.css        # 认证页面样式
│   └── profile.css     # 用户中心样式
├── js/
│   ├── script.js       # 主页面功能
│   ├── data.js         # 共享数据和工具函数
│   ├── list.js         # 列表页面功能
│   ├── detail.js       # 详情页面功能
│   ├── search.js       # 搜索页面功能
│   ├── auth.js         # 用户认证核心功能
│   ├── login.js        # 登录页面功能
│   ├── register.js     # 注册页面功能
│   └── profile.js      # 用户中心功能
└── README.md           # 项目说明
```

## 技术栈

- **HTML5**: 语义化标签和现代HTML特性
- **CSS3**: 
  - Flexbox和Grid布局
  - CSS动画和过渡效果
  - 媒体查询响应式设计
  - CSS变量和渐变
- **JavaScript ES6+**:
  - 模块化代码结构
  - 事件处理和DOM操作
  - 数组方法和现代语法
  - 交互式功能实现

## 快速开始

1. **下载项目文件**
   ```bash
   # 确保所有文件都在同一目录下
   index.html
   css/style.css
   js/script.js
   ```

2. **打开网站**
   - 直接双击 `index.html` 文件
   - 或使用本地服务器（推荐）

3. **使用本地服务器（推荐）**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   
   # 使用PHP
   php -S localhost:8000
   ```

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 功能说明

### 页面导航
- **首页**: 轮播图、分类筛选、热门视频展示
- **列表页**: 分类浏览、排序、分页、视图切换
- **详情页**: 视频播放、详细信息、评论、相关推荐
- **搜索页**: 高级搜索、搜索建议、热门关键词
- **登录页**: 用户登录、社交登录、忘记密码
- **注册页**: 用户注册、密码强度检测、表单验证
- **用户中心**: 个人信息、观看历史、收藏管理、账户设置

### 视频播放
- 点击任意视频卡片跳转到详情页
- 支持全屏播放和音量控制
- 视频信息、标签、评论完整展示

### 用户系统
- **注册登录**: 邮箱注册、密码强度检测、记住登录状态
- **个人资料**: 头像、用户名、邮箱、手机号、个人简介管理
- **播放历史**: 自动记录观看进度，支持历史回看和清空
- **个人收藏**: 收藏/取消收藏视频，收藏列表管理
- **隐私设置**: 观看历史记录开关、个性化推荐设置
- **账户安全**: 密码修改、账户删除等安全操作

### 搜索和筛选
- 全站搜索功能，支持关键词高亮
- 高级搜索：分类、时长、发布时间筛选
- 搜索建议和热门关键词推荐
- 支持组合搜索和筛选条件管理

### 响应式布局
- **桌面端**: 多列网格布局，完整导航栏
- **平板端**: 适中的网格布局，优化的间距
- **手机端**: 单列布局，汉堡菜单导航

### 交互特性
- 轮播图自动播放（5秒间隔）
- 卡片悬停效果
- 平滑滚动动画
- 加载更多功能

## 自定义配置

### 修改视频数据
编辑 `js/script.js` 文件中的 `videoData` 数组：

```javascript
const videoData = [
    {
        id: 1,
        title: "视频标题",
        category: "romance", // romance, comedy, drama, action, suspense
        thumbnail: "缩略图URL",
        duration: "15:30",
        views: "128万",
        description: "视频描述",
        videoUrl: "视频文件URL"
    }
    // 添加更多视频...
];
```

### 修改样式主题
编辑 `css/style.css` 文件中的颜色变量：

```css
/* 主要渐变色 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 强调色 */
background: linear-gradient(45deg, #ff6b6b, #ee5a24);
```

## 部署说明

### 静态网站托管
- **GitHub Pages**: 免费托管静态网站
- **Netlify**: 拖拽部署，自动构建
- **Vercel**: 快速部署，全球CDN

### 服务器部署
1. 将所有文件上传到服务器
2. 确保文件权限正确
3. 配置Web服务器指向index.html

## 开发建议

### 添加新功能
- 用户登录和收藏功能
- 评论和评分系统
- 视频上传管理
- 播放历史记录

### 性能优化
- 图片压缩和WebP格式
- CSS和JS文件压缩
- CDN加速静态资源
- 服务端渲染（SSR）

### SEO优化
- 添加meta标签
- 结构化数据标记
- 网站地图生成
- 页面加载速度优化

## 许可证

MIT License - 可自由使用和修改

## 联系方式

如有问题或建议，欢迎联系：
- 邮箱: <EMAIL>
- 项目地址: [GitHub仓库链接]

---

**享受观看短剧的乐趣！** 🎬✨
