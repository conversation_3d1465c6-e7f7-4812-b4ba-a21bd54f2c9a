<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频详情 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/detail.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="#" class="nav-link">热门</a>
                <a href="list.html" class="nav-link">分类</a>
                <a href="#" class="nav-link">收藏</a>
            </div>
            <div class="nav-search">
                <input type="text" placeholder="搜索短剧..." id="searchInput">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
            </div>
            <div class="nav-user" id="navUser">
                <!-- 用户信息将通过JavaScript动态生成 -->
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 面包屑导航 -->
        <section class="breadcrumb-section">
            <div class="container">
                <nav class="breadcrumb">
                    <a href="index.html"><i class="fas fa-home"></i> 首页</a>
                    <span class="separator">/</span>
                    <a href="list.html" id="categoryLink">分类</a>
                    <span class="separator">/</span>
                    <span class="current" id="currentTitle">视频详情</span>
                </nav>
            </div>
        </section>

        <!-- 视频播放区域 -->
        <section class="video-player-section">
            <div class="container">
                <div class="player-wrapper">
                    <div class="video-player">
                        <video id="mainVideoPlayer" controls poster="">
                            <source src="" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        <div class="player-overlay" id="playerOverlay">
                            <div class="play-button" id="playButton">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 视频信息区域 -->
        <section class="video-info-section">
            <div class="container">
                <div class="video-details">
                    <div class="video-main-info">
                        <h1 class="video-title" id="videoTitle">视频标题</h1>
                        <div class="video-meta">
                            <div class="meta-item">
                                <i class="fas fa-eye"></i>
                                <span id="videoViews">0</span> 次观看
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span id="videoDuration">00:00</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span id="videoDate">发布时间</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-tag"></i>
                                <span class="category-tag" id="videoCategory">分类</span>
                            </div>
                        </div>
                        <div class="video-actions">
                            <button class="action-btn primary" id="likeBtn">
                                <i class="fas fa-thumbs-up"></i>
                                <span>点赞</span>
                                <span class="count" id="likeCount">0</span>
                            </button>
                            <button class="action-btn" id="favoriteBtn">
                                <i class="fas fa-heart"></i>
                                <span>收藏</span>
                            </button>
                            <button class="action-btn" id="shareBtn">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </button>
                            <button class="action-btn" id="downloadBtn">
                                <i class="fas fa-download"></i>
                                <span>下载</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="video-description">
                        <h3>剧情简介</h3>
                        <div class="description-content" id="videoDescription">
                            <p>视频描述内容...</p>
                        </div>
                        <button class="expand-btn" id="expandBtn">展开全部</button>
                    </div>
                    
                    <div class="video-tags">
                        <h3>相关标签</h3>
                        <div class="tags-list" id="videoTags">
                            <!-- 标签将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 评论区域 -->
        <section class="comments-section">
            <div class="container">
                <div class="comments-header">
                    <h3>评论 (<span id="commentCount">0</span>)</h3>
                    <div class="comment-sort">
                        <select id="commentSort">
                            <option value="latest">最新</option>
                            <option value="hot">最热</option>
                        </select>
                    </div>
                </div>
                
                <div class="comment-form">
                    <div class="comment-input">
                        <textarea placeholder="写下你的评论..." id="commentText"></textarea>
                        <div class="comment-actions">
                            <button class="submit-btn" id="submitComment">发表评论</button>
                        </div>
                    </div>
                </div>
                
                <div class="comments-list" id="commentsList">
                    <!-- 评论将通过JavaScript生成 -->
                </div>
                
                <div class="load-more-comments">
                    <button class="load-more-btn" id="loadMoreComments">加载更多评论</button>
                </div>
            </div>
        </section>

        <!-- 相关推荐 -->
        <section class="related-section">
            <div class="container">
                <h3 class="section-title">相关推荐</h3>
                <div class="related-grid" id="relatedGrid">
                    <!-- 相关视频将通过JavaScript生成 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 分享模态框 -->
    <div class="share-modal" id="shareModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>分享到</h3>
                <button class="close-btn" id="closeShareModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="share-options">
                    <button class="share-option" data-platform="wechat">
                        <i class="fab fa-weixin"></i>
                        <span>微信</span>
                    </button>
                    <button class="share-option" data-platform="weibo">
                        <i class="fab fa-weibo"></i>
                        <span>微博</span>
                    </button>
                    <button class="share-option" data-platform="qq">
                        <i class="fab fa-qq"></i>
                        <span>QQ</span>
                    </button>
                    <button class="share-option" data-platform="link">
                        <i class="fas fa-link"></i>
                        <span>复制链接</span>
                    </button>
                </div>
                <div class="share-url">
                    <input type="text" id="shareUrl" readonly>
                    <button class="copy-btn" id="copyUrlBtn">复制</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <p>专业的短剧视频平台，为您提供优质的短剧内容。</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>邮箱: <EMAIL></p>
                    <p>电话: 400-123-4567</p>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-wechat"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 短剧TV. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script src="js/dataLoader.js"></script>
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/detail.js"></script>
</body>
</html>
