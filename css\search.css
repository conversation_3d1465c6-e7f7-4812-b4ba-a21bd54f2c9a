/* 搜索页面专用样式 */

/* 搜索结果标题 */
.search-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
}

.search-info {
    text-align: center;
    margin-bottom: 2rem;
}

.search-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.search-title span {
    color: #ffd700;
}

.search-stats {
    font-size: 1rem;
    opacity: 0.9;
}

.search-time {
    opacity: 0.7;
    font-size: 0.9rem;
}

/* 高级搜索 */
.advanced-search {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1rem;
    display: none;
}

.advanced-search.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.search-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.search-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-field label {
    font-weight: 500;
    font-size: 0.95rem;
}

.search-field input,
.search-field select {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.9);
    color: #333;
    font-size: 0.95rem;
}

.search-field input:focus,
.search-field select:focus {
    outline: none;
    border-color: #ffd700;
    background: white;
}

.search-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.search-btn,
.reset-btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-btn {
    background: #ffd700;
    color: #333;
}

.search-btn:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.reset-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.reset-btn:hover {
    background: rgba(255,255,255,0.3);
}

.toggle-advanced {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.toggle-advanced:hover {
    background: rgba(255,255,255,0.3);
}

/* 搜索建议 */
.search-suggestions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 1rem 0;
}

.suggestions-content h3 {
    color: #856404;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.suggestion-item {
    background: white;
    color: #667eea;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.suggestion-item:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 筛选标签 */
.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.filter-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag .remove-tag {
    background: rgba(255,255,255,0.3);
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.filter-tag .remove-tag:hover {
    background: rgba(255,255,255,0.5);
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: auto;
}

/* 搜索结果 */
.results-section {
    padding: 2rem 0;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.results-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.results-grid.list-view .video-card {
    display: flex;
    height: 200px;
}

.results-grid.list-view .video-thumbnail {
    width: 300px;
    height: 200px;
    flex-shrink: 0;
}

.results-grid.list-view .video-info {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.results-grid.list-view .video-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.results-grid.list-view .video-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 搜索关键词高亮 */
.highlight {
    background: #ffd700;
    color: #333;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 500;
}

/* 无结果状态 */
.no-results {
    text-align: center;
    padding: 4rem 0;
    color: #6c757d;
}

.no-results-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.no-results p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.search-tips {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem auto;
    max-width: 500px;
    text-align: left;
}

.search-tips h4 {
    color: #495057;
    margin-bottom: 1rem;
}

.search-tips ul {
    list-style: none;
    padding: 0;
}

.search-tips li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.search-tips li::before {
    content: '•';
    color: #667eea;
    position: absolute;
    left: 0;
}

/* 热门搜索 */
.hot-searches {
    background: #f8f9fa;
    padding: 2rem 0;
    margin-top: 2rem;
}

.hot-searches h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.hot-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.hot-keyword {
    background: white;
    color: #495057;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.hot-keyword:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

.hot-keyword.trending::after {
    content: 'HOT';
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff6b6b;
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .results-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .search-title {
        font-size: 1.5rem;
    }
    
    .search-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .search-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .sort-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        margin-left: 0;
    }
    
    .filter-controls {
        flex-direction: column;
        gap: 1rem;
    }
    
    .results-grid.list-view .video-card {
        flex-direction: column;
        height: auto;
    }
    
    .results-grid.list-view .video-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .hot-keywords {
        gap: 0.5rem;
    }
    
    .hot-keyword {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .search-header {
        padding: 1.5rem 0;
    }
    
    .search-title {
        font-size: 1.3rem;
    }
    
    .advanced-search {
        padding: 1rem;
    }
    
    .search-field input,
    .search-field select {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .search-btn,
    .reset-btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filter-tags {
        gap: 0.5rem;
    }
    
    .filter-tag {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}
