// 列表页面JavaScript

// DOM元素
const videoGrid = document.getElementById('videoGrid');
const filterBtns = document.querySelectorAll('.filter-btn');
const sortSelect = document.getElementById('sortSelect');
const viewBtns = document.querySelectorAll('.view-btn');
const pageTitle = document.getElementById('pageTitle');
const pageSubtitle = document.getElementById('pageSubtitle');
const totalCount = document.getElementById('totalCount');
const currentCategory = document.getElementById('currentCategory');
const loading = document.getElementById('loading');
const emptyState = document.getElementById('emptyState');
const pagination = document.getElementById('pagination');
const prevPage = document.getElementById('prevPage');
const nextPage = document.getElementById('nextPage');
const pageNumbers = document.getElementById('pageNumbers');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const navToggle = document.getElementById('navToggle');

// 状态变量
let currentCategoryFilter = 'all';
let currentSort = 'latest';
let currentView = 'grid';
let currentPage = 1;
let itemsPerPage = 12;
let filteredVideos = [];

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    bindEvents();
});

// 监听数据加载完成事件
document.addEventListener('dataLoaded', function() {
    initializePage();
});

// 初始化页面
function initializePage() {
    // 从URL参数获取分类
    const urlParams = new URLSearchParams(window.location.search);
    const category = urlParams.get('category') || 'all';
    const keyword = urlParams.get('q') || '';
    
    currentCategoryFilter = category;
    
    // 更新页面标题和分类
    updatePageInfo(category);
    
    // 设置搜索框值
    if (keyword) {
        searchInput.value = keyword;
    }
    
    // 设置活动的筛选按钮
    filterBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === category);
    });
    
    // 加载视频列表
    loadVideos();
}

// 更新页面信息
function updatePageInfo(category) {
    // 确保categoryInfo已加载
    if (!window.categoryInfo) {
        return;
    }

    const categoryData = categoryInfo[category];
    if (categoryData) {
        pageTitle.textContent = categoryData.name + '短剧';
        pageSubtitle.textContent = categoryData.description;
        currentCategory.textContent = categoryData.name;
    }
}

// 加载视频列表
function loadVideos() {
    showLoading(true);

    // 确保数据已加载
    if (!window.utils || !videoData || videoData.length === 0) {
        setTimeout(loadVideos, 100); // 等待数据加载
        return;
    }

    // 模拟加载延迟
    setTimeout(() => {
        const keyword = searchInput.value.trim();
        filteredVideos = utils.searchVideos(keyword, currentCategoryFilter === 'all' ? '' : currentCategoryFilter, currentSort);
        
        updateTotalCount();
        renderVideos();
        renderPagination();
        showLoading(false);
        
        // 显示空状态或视频列表
        if (filteredVideos.length === 0) {
            showEmptyState(true);
        } else {
            showEmptyState(false);
        }
    }, 500);
}

// 渲染视频列表
function renderVideos() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const videosToShow = filteredVideos.slice(startIndex, endIndex);
    
    videoGrid.innerHTML = '';
    videoGrid.className = `video-grid ${currentView === 'list' ? 'list-view' : ''}`;
    
    videosToShow.forEach(video => {
        const cardElement = document.createElement('div');
        cardElement.innerHTML = utils.createVideoCard(video, currentView === 'list', searchInput.value.trim());
        const card = cardElement.firstElementChild;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            window.location.href = `detail.html?id=${video.id}`;
        });
        
        videoGrid.appendChild(card);
    });
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(filteredVideos.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    
    // 更新上一页/下一页按钮
    prevPage.disabled = currentPage === 1;
    nextPage.disabled = currentPage === totalPages;
    
    // 生成页码
    pageNumbers.innerHTML = '';
    
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // 第一页
    if (startPage > 1) {
        pageNumbers.appendChild(createPageButton(1));
        if (startPage > 2) {
            pageNumbers.appendChild(createEllipsis());
        }
    }
    
    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        pageNumbers.appendChild(createPageButton(i));
    }
    
    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pageNumbers.appendChild(createEllipsis());
        }
        pageNumbers.appendChild(createPageButton(totalPages));
    }
}

// 创建页码按钮
function createPageButton(pageNum) {
    const button = document.createElement('button');
    button.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
    button.textContent = pageNum;
    button.addEventListener('click', () => {
        currentPage = pageNum;
        renderVideos();
        renderPagination();
        scrollToTop();
    });
    return button;
}

// 创建省略号
function createEllipsis() {
    const ellipsis = document.createElement('span');
    ellipsis.className = 'page-ellipsis';
    ellipsis.textContent = '...';
    return ellipsis;
}

// 更新总数统计
function updateTotalCount() {
    totalCount.textContent = `共 ${filteredVideos.length} 部短剧`;
}

// 显示/隐藏加载状态
function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
    videoGrid.style.display = show ? 'none' : 'grid';
}

// 显示/隐藏空状态
function showEmptyState(show) {
    emptyState.style.display = show ? 'block' : 'none';
    videoGrid.style.display = show ? 'none' : 'grid';
    pagination.style.display = show ? 'none' : 'flex';
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 绑定事件
function bindEvents() {
    // 分类筛选
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentCategoryFilter = btn.dataset.category;
            currentPage = 1;
            updatePageInfo(currentCategoryFilter);
            loadVideos();
            
            // 更新URL
            const url = new URL(window.location);
            if (currentCategoryFilter === 'all') {
                url.searchParams.delete('category');
            } else {
                url.searchParams.set('category', currentCategoryFilter);
            }
            window.history.pushState({}, '', url);
        });
    });
    
    // 排序
    sortSelect.addEventListener('change', () => {
        currentSort = sortSelect.value;
        currentPage = 1;
        loadVideos();
    });
    
    // 视图切换
    viewBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            viewBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentView = btn.dataset.view;
            renderVideos();
        });
    });
    
    // 搜索
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 分页
    prevPage.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderVideos();
            renderPagination();
            scrollToTop();
        }
    });
    
    nextPage.addEventListener('click', () => {
        const totalPages = Math.ceil(filteredVideos.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderVideos();
            renderPagination();
            scrollToTop();
        }
    });
    
    // 移动端导航
    navToggle.addEventListener('click', () => {
        const navMenu = document.querySelector('.nav-menu');
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });
    
    // 点击导航链接后关闭移动端菜单
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
            document.body.classList.remove('nav-open');
        });
    });
}

// 执行搜索
function performSearch() {
    const keyword = searchInput.value.trim();
    if (keyword) {
        // 跳转到搜索页面
        window.location.href = `search.html?q=${encodeURIComponent(keyword)}`;
    } else {
        currentPage = 1;
        loadVideos();
    }
}
