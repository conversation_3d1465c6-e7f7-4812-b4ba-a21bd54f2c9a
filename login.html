<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="list.html?category=all" class="nav-link">热门</a>
                <a href="list.html" class="nav-link">分类</a>
            </div>
            <div class="nav-auth">
                <a href="register.html" class="auth-link">注册</a>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>欢迎回来</h1>
                    <p>登录您的账户，继续观看精彩短剧</p>
                </div>
                
                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="请输入密码" required>
                            <button type="button" class="toggle-password" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark"></span>
                            记住我
                        </label>
                        <a href="#" class="forgot-password">忘记密码？</a>
                    </div>
                    
                    <button type="submit" class="auth-btn primary" id="loginBtn">
                        <span class="btn-text">登录</span>
                        <div class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                    
                    <div class="auth-divider">
                        <span>或者</span>
                    </div>
                    
                    <div class="social-login">
                        <button type="button" class="social-btn wechat">
                            <i class="fab fa-weixin"></i>
                            微信登录
                        </button>
                        <button type="button" class="social-btn qq">
                            <i class="fab fa-qq"></i>
                            QQ登录
                        </button>
                    </div>
                </form>
                
                <div class="auth-footer">
                    <p>还没有账户？ <a href="register.html">立即注册</a></p>
                </div>
            </div>
            
            <!-- 登录背景装饰 -->
            <div class="auth-decoration">
                <div class="decoration-item item-1"></div>
                <div class="decoration-item item-2"></div>
                <div class="decoration-item item-3"></div>
            </div>
        </div>
    </main>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/login.js"></script>
</body>
</html>
