// 登录页面JavaScript

// DOM元素
const loginForm = document.getElementById('loginForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const rememberMeInput = document.getElementById('rememberMe');
const togglePasswordBtn = document.getElementById('togglePassword');
const loginBtn = document.getElementById('loginBtn');
const navToggle = document.getElementById('navToggle');

// 状态变量
let isLoading = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果已经登录，跳转到首页
    if (authManager.isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    bindEvents();
    setupFormValidation();
});

// 绑定事件
function bindEvents() {
    // 表单提交
    loginForm.addEventListener('submit', handleLogin);
    
    // 密码显示/隐藏切换
    togglePasswordBtn.addEventListener('click', togglePasswordVisibility);
    
    // 输入框实时验证
    emailInput.addEventListener('input', validateEmail);
    passwordInput.addEventListener('input', validatePassword);
    
    // 移动端导航
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // 社交登录按钮
    document.querySelectorAll('.social-btn').forEach(btn => {
        btn.addEventListener('click', handleSocialLogin);
    });
    
    // 忘记密码链接
    const forgotPasswordLink = document.querySelector('.forgot-password');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    const rememberMe = rememberMeInput.checked;
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    setLoading(true);
    
    try {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 执行登录
        const user = authManager.login(email, password, rememberMe);
        
        showToast('登录成功！', 'success');
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
            // 检查是否有重定向URL
            const urlParams = new URLSearchParams(window.location.search);
            const redirect = urlParams.get('redirect') || 'index.html';
            window.location.href = redirect;
        }, 1000);
        
    } catch (error) {
        showToast(error.message, 'error');
        setLoading(false);
    }
}

// 设置加载状态
function setLoading(loading) {
    isLoading = loading;
    loginBtn.disabled = loading;
    
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoading = loginBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'flex';
        loginBtn.classList.add('loading');
    } else {
        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
        loginBtn.classList.remove('loading');
    }
}

// 切换密码可见性
function togglePasswordVisibility() {
    const type = passwordInput.type === 'password' ? 'text' : 'password';
    passwordInput.type = type;
    
    const icon = togglePasswordBtn.querySelector('i');
    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
}

// 表单验证
function validateForm() {
    let isValid = true;
    
    // 验证邮箱
    if (!validateEmail()) {
        isValid = false;
    }
    
    // 验证密码
    if (!validatePassword()) {
        isValid = false;
    }
    
    return isValid;
}

// 验证邮箱
function validateEmail() {
    const email = emailInput.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    removeFieldError(emailInput);
    
    if (!email) {
        showFieldError(emailInput, '请输入邮箱地址');
        return false;
    }
    
    if (!emailRegex.test(email)) {
        showFieldError(emailInput, '请输入有效的邮箱地址');
        return false;
    }
    
    return true;
}

// 验证密码
function validatePassword() {
    const password = passwordInput.value;
    
    removeFieldError(passwordInput);
    
    if (!password) {
        showFieldError(passwordInput, '请输入密码');
        return false;
    }
    
    if (password.length < 6) {
        showFieldError(passwordInput, '密码长度至少6位');
        return false;
    }
    
    return true;
}

// 显示字段错误
function showFieldError(input, message) {
    const wrapper = input.closest('.input-wrapper');
    const formGroup = input.closest('.form-group');
    
    // 移除现有错误
    removeFieldError(input);
    
    // 添加错误样式
    wrapper.classList.add('error');
    
    // 添加错误消息
    const errorEl = document.createElement('div');
    errorEl.className = 'field-error';
    errorEl.textContent = message;
    formGroup.appendChild(errorEl);
}

// 移除字段错误
function removeFieldError(input) {
    const wrapper = input.closest('.input-wrapper');
    const formGroup = input.closest('.form-group');
    
    wrapper.classList.remove('error');
    
    const existingError = formGroup.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// 设置表单验证样式
function setupFormValidation() {
    // 添加错误样式到CSS
    const style = document.createElement('style');
    style.textContent = `
        .input-wrapper.error input {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .field-error {
            color: #dc3545;
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }
    `;
    document.head.appendChild(style);
}

// 处理社交登录
function handleSocialLogin(e) {
    const platform = e.currentTarget.classList.contains('wechat') ? '微信' : 'QQ';
    showToast(`${platform}登录功能暂未开放`, 'info');
}

// 处理忘记密码
function handleForgotPassword(e) {
    e.preventDefault();
    
    const email = prompt('请输入您的邮箱地址，我们将发送重置密码的链接：');
    
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(email)) {
            showToast('密码重置链接已发送到您的邮箱', 'success');
        } else {
            showToast('请输入有效的邮箱地址', 'error');
        }
    }
}

// 键盘事件处理
document.addEventListener('keydown', (e) => {
    // Enter键提交表单
    if (e.key === 'Enter' && !isLoading) {
        const activeElement = document.activeElement;
        if (activeElement === emailInput || activeElement === passwordInput) {
            handleLogin(e);
        }
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，检查登录状态
        if (authManager.isLoggedIn()) {
            window.location.href = 'index.html';
        }
    }
});
