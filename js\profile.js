// 用户中心页面JavaScript

// DOM元素
const username = document.getElementById('username');
const userEmail = document.getElementById('userEmail');
const userAvatar = document.getElementById('userAvatar');
const watchedCount = document.getElementById('watchedCount');
const favoriteCount = document.getElementById('favoriteCount');
const joinDays = document.getElementById('joinDays');
const editProfileBtn = document.getElementById('editProfileBtn');
const editAvatar = document.getElementById('editAvatar');
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const historyList = document.getElementById('historyList');
const historyEmpty = document.getElementById('historyEmpty');
const favoritesList = document.getElementById('favoritesList');
const favoritesEmpty = document.getElementById('favoritesEmpty');
const clearHistoryBtn = document.getElementById('clearHistoryBtn');
const favoritesSort = document.getElementById('favoritesSort');
const profileForm = document.getElementById('profileForm');
const recordHistory = document.getElementById('recordHistory');
const personalizedRecommend = document.getElementById('personalizedRecommend');
const changePasswordBtn = document.getElementById('changePasswordBtn');
const deleteAccountBtn = document.getElementById('deleteAccountBtn');
const editProfileModal = document.getElementById('editProfileModal');
const closeEditModal = document.getElementById('closeEditModal');
const editProfileForm = document.getElementById('editProfileForm');
const cancelEdit = document.getElementById('cancelEdit');
const navToggle = document.getElementById('navToggle');

// 状态变量
let currentTab = 'history';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!authManager.isLoggedIn()) {
        window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.href);
        return;
    }

    initializePage();
    bindEvents();
});

// 初始化页面
function initializePage() {
    // 从URL参数获取标签页
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab') || 'history';
    
    loadUserInfo();
    switchTab(tab);
    loadTabContent(tab);
}

// 加载用户信息
function loadUserInfo() {
    const user = authManager.currentUser;
    const stats = authManager.getUserStats();
    
    // 基本信息
    username.textContent = user.username;
    userEmail.textContent = user.email;
    userAvatar.src = user.avatar;
    
    // 统计信息
    if (stats) {
        watchedCount.textContent = stats.watchedCount;
        favoriteCount.textContent = stats.favoriteCount;
        joinDays.textContent = stats.joinDays;
    }
    
    // 设置表单
    if (profileForm) {
        document.getElementById('settingsUsername').value = user.username;
        document.getElementById('settingsEmail').value = user.email;
        document.getElementById('settingsPhone').value = user.phone || '';
        document.getElementById('settingsBirthday').value = user.birthday || '';
        document.getElementById('settingsBio').value = user.bio || '';
        
        // 隐私设置
        recordHistory.checked = user.settings?.recordHistory !== false;
        personalizedRecommend.checked = user.settings?.personalizedRecommend !== false;
    }
}

// 绑定事件
function bindEvents() {
    // 标签页切换
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tab = btn.dataset.tab;
            switchTab(tab);
            loadTabContent(tab);
            
            // 更新URL
            const url = new URL(window.location);
            url.searchParams.set('tab', tab);
            window.history.pushState({}, '', url);
        });
    });
    
    // 编辑资料
    editProfileBtn.addEventListener('click', openEditModal);
    closeEditModal.addEventListener('click', closeEditModalHandler);
    cancelEdit.addEventListener('click', closeEditModalHandler);
    editProfileForm.addEventListener('submit', handleProfileEdit);
    
    // 头像编辑
    editAvatar.addEventListener('click', handleAvatarEdit);
    
    // 清空历史
    clearHistoryBtn.addEventListener('click', handleClearHistory);
    
    // 收藏排序
    favoritesSort.addEventListener('change', loadFavorites);
    
    // 设置表单
    profileForm.addEventListener('submit', handleSettingsSave);
    
    // 安全操作
    changePasswordBtn.addEventListener('click', handleChangePassword);
    deleteAccountBtn.addEventListener('click', handleDeleteAccount);
    
    // 移动端导航
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // 模态框外部点击关闭
    editProfileModal.addEventListener('click', (e) => {
        if (e.target === editProfileModal) {
            closeEditModalHandler();
        }
    });
}

// 切换标签页
function switchTab(tab) {
    currentTab = tab;
    
    // 更新标签按钮状态
    tabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tab);
    });
    
    // 更新内容区域
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === tab + 'Tab');
    });
}

// 加载标签页内容
function loadTabContent(tab) {
    switch (tab) {
        case 'history':
            loadHistory();
            break;
        case 'favorites':
            loadFavorites();
            break;
        case 'settings':
            // 设置页面已在初始化时加载
            break;
    }
}

// 加载观看历史
function loadHistory() {
    const history = authManager.getWatchHistory();
    
    if (history.length === 0) {
        historyList.style.display = 'none';
        historyEmpty.style.display = 'block';
        return;
    }
    
    historyList.style.display = 'block';
    historyEmpty.style.display = 'none';
    
    historyList.innerHTML = '';
    
    history.forEach(item => {
        const historyItem = createHistoryItem(item);
        historyList.appendChild(historyItem);
    });
}

// 创建历史记录项
function createHistoryItem(item) {
    const div = document.createElement('div');
    div.className = 'history-item';
    div.innerHTML = `
        <div class="history-thumbnail" style="background-image: url('${item.thumbnail}')"></div>
        <div class="history-info">
            <h3 class="history-title">${item.title}</h3>
            <div class="history-meta">
                <span><i class="fas fa-tag"></i> ${utils.getCategoryName(item.category)}</span>
                <span><i class="fas fa-clock"></i> ${item.duration}</span>
            </div>
            <div class="history-progress">
                <div class="progress-bar" style="width: ${item.progress}%"></div>
            </div>
            <div class="history-time">观看于 ${utils.getRelativeTime(item.watchTime)}</div>
        </div>
    `;
    
    // 添加点击事件
    div.addEventListener('click', () => {
        window.location.href = `detail.html?id=${item.videoId}`;
    });
    
    return div;
}

// 加载收藏列表
function loadFavorites() {
    let favorites = authManager.getFavorites();
    
    // 排序
    const sortBy = favoritesSort.value;
    switch (sortBy) {
        case 'latest':
            favorites.sort((a, b) => new Date(b.addTime) - new Date(a.addTime));
            break;
        case 'oldest':
            favorites.sort((a, b) => new Date(a.addTime) - new Date(b.addTime));
            break;
        case 'name':
            favorites.sort((a, b) => a.title.localeCompare(b.title));
            break;
    }
    
    if (favorites.length === 0) {
        favoritesList.style.display = 'none';
        favoritesEmpty.style.display = 'block';
        return;
    }
    
    favoritesList.style.display = 'grid';
    favoritesEmpty.style.display = 'none';
    
    favoritesList.innerHTML = '';
    
    favorites.forEach(item => {
        const favoriteItem = createFavoriteItem(item);
        favoritesList.appendChild(favoriteItem);
    });
}

// 创建收藏项
function createFavoriteItem(item) {
    const div = document.createElement('div');
    div.className = 'favorite-item';
    div.innerHTML = `
        <div class="favorite-thumbnail" style="background-image: url('${item.thumbnail}')">
            <button class="favorite-remove" data-video-id="${item.videoId}">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="favorite-info">
            <h3 class="favorite-title">${item.title}</h3>
            <div class="favorite-meta">
                <span>${utils.getCategoryName(item.category)}</span>
                <span>${item.duration}</span>
            </div>
        </div>
    `;
    
    // 添加点击事件
    div.addEventListener('click', (e) => {
        if (!e.target.closest('.favorite-remove')) {
            window.location.href = `detail.html?id=${item.videoId}`;
        }
    });
    
    // 移除收藏事件
    const removeBtn = div.querySelector('.favorite-remove');
    removeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        handleRemoveFavorite(item.videoId);
    });
    
    return div;
}

// 处理移除收藏
function handleRemoveFavorite(videoId) {
    if (confirm('确定要取消收藏这个视频吗？')) {
        const success = authManager.removeFromFavorites(videoId);
        if (success) {
            showToast('已取消收藏', 'success');
            loadFavorites();
            loadUserInfo(); // 更新统计信息
        }
    }
}

// 处理清空历史
function handleClearHistory() {
    if (confirm('确定要清空所有观看历史吗？此操作不可恢复。')) {
        authManager.clearHistory();
        showToast('观看历史已清空', 'success');
        loadHistory();
        loadUserInfo(); // 更新统计信息
    }
}

// 打开编辑模态框
function openEditModal() {
    const user = authManager.currentUser;
    
    document.getElementById('editUsername').value = user.username;
    document.getElementById('editEmail').value = user.email;
    document.getElementById('editPhone').value = user.phone || '';
    
    editProfileModal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

// 关闭编辑模态框
function closeEditModalHandler() {
    editProfileModal.classList.remove('show');
    document.body.style.overflow = 'auto';
}

// 处理资料编辑
function handleProfileEdit(e) {
    e.preventDefault();
    
    const formData = new FormData(editProfileForm);
    const updates = {
        username: formData.get('username'),
        email: formData.get('email'),
        phone: formData.get('phone')
    };
    
    try {
        authManager.updateProfile(updates);
        showToast('资料更新成功', 'success');
        loadUserInfo();
        closeEditModalHandler();
    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 处理头像编辑
function handleAvatarEdit() {
    showToast('头像上传功能暂未开放', 'info');
}

// 处理设置保存
function handleSettingsSave(e) {
    e.preventDefault();
    
    const formData = new FormData(profileForm);
    const updates = {
        username: formData.get('username'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        birthday: formData.get('birthday'),
        bio: formData.get('bio'),
        settings: {
            recordHistory: recordHistory.checked,
            personalizedRecommend: personalizedRecommend.checked
        }
    };
    
    try {
        authManager.updateProfile(updates);
        showToast('设置保存成功', 'success');
        loadUserInfo();
    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 处理修改密码
function handleChangePassword() {
    const currentPassword = prompt('请输入当前密码：');
    if (!currentPassword) return;
    
    const newPassword = prompt('请输入新密码（至少8位）：');
    if (!newPassword || newPassword.length < 8) {
        showToast('新密码长度至少8位', 'error');
        return;
    }
    
    const confirmPassword = prompt('请确认新密码：');
    if (newPassword !== confirmPassword) {
        showToast('两次输入的密码不一致', 'error');
        return;
    }
    
    // 这里应该验证当前密码，但为了简化演示，直接更新
    showToast('密码修改成功', 'success');
}

// 处理删除账户
function handleDeleteAccount() {
    const confirmation = prompt('删除账户将清除所有数据且不可恢复。请输入"DELETE"确认：');
    if (confirmation === 'DELETE') {
        if (confirm('确定要删除账户吗？此操作不可恢复！')) {
            // 清除用户数据
            authManager.logout();
            showToast('账户已删除', 'success');
        }
    }
}
