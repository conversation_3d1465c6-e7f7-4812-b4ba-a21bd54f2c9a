// 模拟视频数据
const videoData = [
    {
        id: 1,
        title: "霸道总裁的小娇妻",
        category: "romance",
        thumbnail: "https://via.placeholder.com/300x200/ff6b6b/ffffff?text=霸道总裁",
        duration: "15:30",
        views: "128万",
        description: "一个关于霸道总裁和小娇妻的浪漫爱情故事...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 2,
        title: "搞笑日常生活",
        category: "comedy",
        thumbnail: "https://via.placeholder.com/300x200/2ecc71/ffffff?text=搞笑日常",
        duration: "12:45",
        views: "89万",
        description: "轻松搞笑的日常生活片段，让你开怀大笑...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 3,
        title: "悬疑推理案件",
        category: "suspense",
        thumbnail: "https://via.placeholder.com/300x200/9b59b6/ffffff?text=悬疑推理",
        duration: "20:15",
        views: "156万",
        description: "扣人心弦的悬疑推理故事，真相只有一个...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 4,
        title: "动作冒险传奇",
        category: "action",
        thumbnail: "https://via.placeholder.com/300x200/e74c3c/ffffff?text=动作冒险",
        duration: "18:20",
        views: "203万",
        description: "激动人心的动作冒险故事，英雄的传奇之路...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 5,
        title: "温情家庭剧",
        category: "drama",
        thumbnail: "https://via.placeholder.com/300x200/f39c12/ffffff?text=家庭剧",
        duration: "22:10",
        views: "95万",
        description: "温暖人心的家庭故事，亲情的力量无穷...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 6,
        title: "校园青春恋曲",
        category: "romance",
        thumbnail: "https://via.placeholder.com/300x200/3498db/ffffff?text=校园青春",
        duration: "16:40",
        views: "167万",
        description: "青春校园的美好恋爱故事，回忆那些年的美好...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 7,
        title: "爆笑喜剧合集",
        category: "comedy",
        thumbnail: "https://via.placeholder.com/300x200/1abc9c/ffffff?text=爆笑喜剧",
        duration: "14:25",
        views: "234万",
        description: "精选爆笑喜剧片段，保证让你笑到肚子疼...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    },
    {
        id: 8,
        title: "古装武侠传说",
        category: "action",
        thumbnail: "https://via.placeholder.com/300x200/8e44ad/ffffff?text=古装武侠",
        duration: "25:30",
        views: "189万",
        description: "江湖恩怨情仇，武侠世界的传奇故事...",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4"
    }
];

// DOM元素
const videoGrid = document.getElementById('videoGrid');
const filterBtns = document.querySelectorAll('.filter-btn');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const videoModal = document.getElementById('videoModal');
const closeModal = document.getElementById('closeModal');
const videoPlayer = document.getElementById('videoPlayer');
const modalTitle = document.getElementById('modalTitle');
const videoDescription = document.getElementById('videoDescription');
const navToggle = document.getElementById('navToggle');

// 轮播图相关
const slides = document.querySelectorAll('.slide');
const dots = document.querySelectorAll('.dot');
const prevBtn = document.querySelector('.prev-btn');
const nextBtn = document.querySelector('.next-btn');

let currentSlide = 0;
let currentCategory = 'all';
let displayedVideos = 6;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    renderVideos();
    initSlider();
    bindEvents();
});

// 渲染视频列表
function renderVideos(category = 'all', searchTerm = '') {
    let filteredVideos = videoData;
    
    // 分类筛选
    if (category !== 'all') {
        filteredVideos = filteredVideos.filter(video => video.category === category);
    }
    
    // 搜索筛选
    if (searchTerm) {
        filteredVideos = filteredVideos.filter(video => 
            video.title.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }
    
    // 限制显示数量
    const videosToShow = filteredVideos.slice(0, displayedVideos);
    
    videoGrid.innerHTML = '';
    
    videosToShow.forEach(video => {
        const videoCard = createVideoCard(video);
        videoGrid.appendChild(videoCard);
    });
    
    // 控制加载更多按钮显示
    if (filteredVideos.length <= displayedVideos) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

// 创建视频卡片
function createVideoCard(video) {
    const card = document.createElement('div');
    card.className = 'video-card';
    card.dataset.videoId = video.id;
    
    card.innerHTML = `
        <div class="video-thumbnail" style="background-image: url('${video.thumbnail}')">
            <div class="play-overlay">
                <i class="fas fa-play"></i>
            </div>
        </div>
        <div class="video-info">
            <h3 class="video-title">${video.title}</h3>
            <div class="video-meta">
                <span class="video-duration">${video.duration}</span>
                <span class="video-views">${video.views}次观看</span>
                <span class="video-category">${getCategoryName(video.category)}</span>
            </div>
        </div>
    `;
    
    // 添加点击事件
    card.addEventListener('click', () => openVideoModal(video));
    
    return card;
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryMap = {
        'romance': '言情',
        'comedy': '喜剧',
        'drama': '剧情',
        'action': '动作',
        'suspense': '悬疑'
    };
    return categoryMap[category] || '其他';
}

// 打开视频播放模态框
function openVideoModal(video) {
    modalTitle.textContent = video.title;
    videoDescription.textContent = video.description;
    videoPlayer.src = video.videoUrl;
    videoModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭视频播放模态框
function closeVideoModal() {
    videoModal.style.display = 'none';
    videoPlayer.pause();
    videoPlayer.src = '';
    document.body.style.overflow = 'auto';
}

// 初始化轮播图
function initSlider() {
    showSlide(currentSlide);
    
    // 自动播放
    setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }, 5000);
}

// 显示指定幻灯片
function showSlide(index) {
    slides.forEach((slide, i) => {
        slide.classList.toggle('active', i === index);
    });
    
    dots.forEach((dot, i) => {
        dot.classList.toggle('active', i === index);
    });
}

// 绑定事件
function bindEvents() {
    // 分类筛选
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentCategory = btn.dataset.category;
            displayedVideos = 6;
            renderVideos(currentCategory, searchInput.value);
        });
    });
    
    // 搜索功能
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 加载更多
    loadMoreBtn.addEventListener('click', () => {
        displayedVideos += 6;
        renderVideos(currentCategory, searchInput.value);
    });
    
    // 模态框关闭
    closeModal.addEventListener('click', closeVideoModal);
    videoModal.addEventListener('click', (e) => {
        if (e.target === videoModal) {
            closeVideoModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && videoModal.style.display === 'block') {
            closeVideoModal();
        }
    });
    
    // 轮播图控制
    prevBtn.addEventListener('click', () => {
        currentSlide = currentSlide === 0 ? slides.length - 1 : currentSlide - 1;
        showSlide(currentSlide);
    });
    
    nextBtn.addEventListener('click', () => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    });
    
    // 轮播图指示器
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
        });
    });
    
    // 移动端导航菜单切换
    navToggle.addEventListener('click', () => {
        const navMenu = document.querySelector('.nav-menu');
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });

    // 点击导航链接后关闭移动端菜单
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
            document.body.classList.remove('nav-open');
        });
    });
}

// 执行搜索
function performSearch() {
    const searchTerm = searchInput.value.trim();
    displayedVideos = 6;
    renderVideos(currentCategory, searchTerm);
}

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 懒加载图片
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 添加页面加载动画
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    lazyLoadImages();
});
