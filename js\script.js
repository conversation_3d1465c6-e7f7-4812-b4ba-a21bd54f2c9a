// 主页面JavaScript - 使用JSON数据

// 数据将从dataLoader.js和data.js加载
// DOM元素
const videoGrid = document.getElementById('videoGrid');
const filterBtns = document.querySelectorAll('.filter-btn');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const videoModal = document.getElementById('videoModal');
const closeModal = document.getElementById('closeModal');
const videoPlayer = document.getElementById('videoPlayer');
const modalTitle = document.getElementById('modalTitle');
const videoDescription = document.getElementById('videoDescription');
const navToggle = document.getElementById('navToggle');

// 轮播图相关
const slides = document.querySelectorAll('.slide');
const dots = document.querySelectorAll('.dot');
const prevBtn = document.querySelector('.prev-btn');
const nextBtn = document.querySelector('.next-btn');

let currentSlide = 0;
let currentCategory = 'all';
let displayedVideos = 6;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initSlider();
    bindEvents();
});

// 监听数据加载完成事件
document.addEventListener('dataLoaded', function() {
    renderVideos();
});

// 渲染视频列表
function renderVideos(category = 'all', searchTerm = '') {
    let filteredVideos = videoData;
    
    // 分类筛选
    if (category !== 'all') {
        filteredVideos = filteredVideos.filter(video => video.category === category);
    }
    
    // 搜索筛选
    if (searchTerm) {
        filteredVideos = filteredVideos.filter(video => 
            video.title.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }
    
    // 限制显示数量
    const videosToShow = filteredVideos.slice(0, displayedVideos);
    
    videoGrid.innerHTML = '';
    
    videosToShow.forEach(video => {
        const videoCard = createVideoCard(video);
        videoGrid.appendChild(videoCard);
    });
    
    // 控制加载更多按钮显示
    if (filteredVideos.length <= displayedVideos) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

// 创建视频卡片
function createVideoCard(video) {
    const card = document.createElement('div');
    card.className = 'video-card';
    card.dataset.videoId = video.id;
    
    card.innerHTML = `
        <div class="video-thumbnail" style="background-image: url('${video.thumbnail}')">
            <div class="play-overlay">
                <i class="fas fa-play"></i>
            </div>
        </div>
        <div class="video-info">
            <h3 class="video-title">${video.title}</h3>
            <div class="video-meta">
                <span class="video-duration">${video.duration}</span>
                <span class="video-views">${video.views}次观看</span>
                <span class="video-category">${getCategoryName(video.category)}</span>
            </div>
        </div>
    `;
    
    // 添加点击事件
    card.addEventListener('click', () => openVideoModal(video));
    
    return card;
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryMap = {
        'romance': '言情',
        'comedy': '喜剧',
        'drama': '剧情',
        'action': '动作',
        'suspense': '悬疑'
    };
    return categoryMap[category] || '其他';
}

// 打开视频播放模态框
function openVideoModal(video) {
    // 添加到播放历史
    if (window.authManager && window.authManager.isLoggedIn()) {
        window.authManager.addToHistory(video, 0);
    }

    // 跳转到详情页面而不是打开模态框
    window.location.href = `detail.html?id=${video.id}`;
}

// 关闭视频播放模态框
function closeVideoModal() {
    videoModal.style.display = 'none';
    videoPlayer.pause();
    videoPlayer.src = '';
    document.body.style.overflow = 'auto';
}

// 初始化轮播图
function initSlider() {
    showSlide(currentSlide);
    
    // 自动播放
    setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }, 5000);
}

// 显示指定幻灯片
function showSlide(index) {
    slides.forEach((slide, i) => {
        slide.classList.toggle('active', i === index);
    });
    
    dots.forEach((dot, i) => {
        dot.classList.toggle('active', i === index);
    });
}

// 绑定事件
function bindEvents() {
    // 分类筛选
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // 如果是查看全部按钮，直接跳转
            if (btn.classList.contains('view-all')) {
                return; // 让链接自然跳转
            }

            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentCategory = btn.dataset.category;
            displayedVideos = 6;
            renderVideos(currentCategory, searchInput.value);
        });
    });
    
    // 搜索功能
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 加载更多
    loadMoreBtn.addEventListener('click', () => {
        displayedVideos += 6;
        renderVideos(currentCategory, searchInput.value);
    });
    
    // 模态框关闭
    closeModal.addEventListener('click', closeVideoModal);
    videoModal.addEventListener('click', (e) => {
        if (e.target === videoModal) {
            closeVideoModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && videoModal.style.display === 'block') {
            closeVideoModal();
        }
    });
    
    // 轮播图控制
    prevBtn.addEventListener('click', () => {
        currentSlide = currentSlide === 0 ? slides.length - 1 : currentSlide - 1;
        showSlide(currentSlide);
    });
    
    nextBtn.addEventListener('click', () => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    });
    
    // 轮播图指示器
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
        });
    });
    
    // 移动端导航菜单切换
    navToggle.addEventListener('click', () => {
        const navMenu = document.querySelector('.nav-menu');
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });

    // 点击导航链接后关闭移动端菜单
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
            document.body.classList.remove('nav-open');
        });
    });
}

// 执行搜索
function performSearch() {
    const searchTerm = searchInput.value.trim();
    if (searchTerm) {
        // 跳转到搜索页面
        window.location.href = `search.html?q=${encodeURIComponent(searchTerm)}`;
    } else {
        displayedVideos = 6;
        renderVideos(currentCategory, searchTerm);
    }
}

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 懒加载图片
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 添加页面加载动画
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    lazyLoadImages();
});
