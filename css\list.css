/* 列表页面专用样式 */

/* 面包屑导航 */
.breadcrumb-section {
    background: white;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #764ba2;
}

.breadcrumb .separator {
    color: #6c757d;
}

.breadcrumb .current {
    color: #495057;
    font-weight: 500;
}

/* 页面标题 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.stats {
    font-size: 0.95rem;
    opacity: 0.8;
}

/* 筛选控制 */
.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group,
.sort-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filter-group label,
.sort-group label {
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
}

.sort-select {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #495057;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-select:focus {
    outline: none;
    border-color: #667eea;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover,
.view-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

/* 视频网格布局 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 列表视图样式 */
.video-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.video-grid.list-view .video-card {
    display: flex;
    height: 200px;
    max-width: none;
}

.video-grid.list-view .video-thumbnail {
    width: 300px;
    height: 200px;
    flex-shrink: 0;
}

.video-grid.list-view .video-info {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-grid.list-view .video-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.video-grid.list-view .video-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-grid.list-view .video-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.video-grid.list-view .video-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 3rem 0;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 0;
    color: #6c757d;
}

.empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 2rem;
}

.back-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.back-btn:hover {
    transform: translateY(-2px);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 3rem;
    padding: 2rem 0;
}

.page-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: #495057;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    width: 40px;
    height: 40px;
    border: 2px solid #e9ecef;
    background: white;
    color: #495057;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.page-number:hover,
.page-number.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.page-ellipsis {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .video-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .header-content h1 {
        font-size: 2rem;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .filter-group,
    .sort-group {
        justify-content: space-between;
    }
    
    .filter-tabs {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .video-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .video-grid.list-view .video-card {
        flex-direction: column;
        height: auto;
    }

    .video-grid.list-view .video-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .page-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .page-numbers {
        order: -1;
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 2rem 0;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }

    .breadcrumb {
        font-size: 0.8rem;
    }

    .filter-group label,
    .sort-group label {
        font-size: 0.9rem;
    }

    .view-toggle {
        justify-content: center;
    }

    .page-numbers {
        gap: 0.25rem;
    }

    .page-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* 视频描述字段 */
.video-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
    display: none; /* 默认在网格视图中隐藏 */
}

.video-grid.list-view .video-description {
    display: block;
}
