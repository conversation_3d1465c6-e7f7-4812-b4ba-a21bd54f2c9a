/* 用户中心页面样式 */

/* 用户信息头部 */
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profile-avatar {
    position: relative;
}

.profile-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255,255,255,0.2);
    object-fit: cover;
}

.avatar-edit {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 36px;
    height: 36px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    color: #667eea;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.avatar-edit:hover {
    background: white;
    transform: scale(1.1);
}

.profile-details {
    flex: 1;
}

.username {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.user-email {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.user-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.edit-profile-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    padding: 0.75rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.edit-profile-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
}

/* 导航标签 */
.profile-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
}

.nav-tabs {
    display: flex;
    gap: 0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1.5rem 2rem;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.tab-btn:hover,
.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* 内容区域 */
.profile-content {
    padding: 3rem 0;
    min-height: 60vh;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.content-header h2 {
    font-size: 1.8rem;
    color: #333;
}

.content-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.action-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: #e9ecef;
    color: #333;
}

.sort-select {
    padding: 0.5rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #495057;
    cursor: pointer;
    font-size: 0.9rem;
}

/* 观看历史 */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.history-thumbnail {
    width: 160px;
    height: 90px;
    border-radius: 8px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
    position: relative;
}

.history-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent 0%, rgba(0,0,0,0.3) 100%);
    border-radius: 8px;
}

.history-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.history-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.history-meta {
    display: flex;
    gap: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.history-progress {
    background: #e9ecef;
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.history-time {
    color: #6c757d;
    font-size: 0.85rem;
}

/* 收藏列表 */
.favorites-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.favorite-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.favorite-item:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.favorite-thumbnail {
    width: 100%;
    height: 160px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.favorite-remove {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
    background: rgba(0,0,0,0.7);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.favorite-item:hover .favorite-remove {
    opacity: 1;
}

.favorite-remove:hover {
    background: #dc3545;
}

.favorite-info {
    padding: 1.5rem;
}

.favorite-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.favorite-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 0;
    color: #6c757d;
}

.empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.empty-action {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    display: inline-block;
    transition: transform 0.3s ease;
}

.empty-action:hover {
    transform: translateY(-2px);
}

/* 设置表单 */
.settings-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.settings-section h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.save-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    align-self: flex-start;
    font-weight: 500;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102,126,234,0.3);
}

/* 设置项 */
.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 0.25rem;
}

.setting-info p {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 开关按钮 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 安全操作 */
.security-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.security-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.security-btn:hover {
    background: #e9ecef;
}

.security-btn.danger {
    color: #dc3545;
    border-color: #dc3545;
}

.security-btn.danger:hover {
    background: #dc3545;
    color: white;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 2rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.cancel-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: #e9ecef;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .favorites-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .username {
        font-size: 2rem;
    }
    
    .user-stats {
        justify-content: center;
    }
    
    .nav-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .tab-btn {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .history-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .history-thumbnail {
        width: 100%;
        height: 180px;
    }
    
    .favorites-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .security-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .profile-header {
        padding: 2rem 0;
    }
    
    .profile-avatar img {
        width: 100px;
        height: 100px;
    }
    
    .username {
        font-size: 1.5rem;
    }
    
    .user-stats {
        gap: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }
    
    .settings-section {
        padding: 1.5rem;
    }

    .favorites-grid {
        grid-template-columns: 1fr;
    }
}
