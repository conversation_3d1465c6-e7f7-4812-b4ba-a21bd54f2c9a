// 用户认证系统核心功能

// 用户数据存储键名
const USER_STORAGE_KEY = 'shortdrama_user';
const USERS_STORAGE_KEY = 'shortdrama_users';
const HISTORY_STORAGE_KEY = 'shortdrama_history';
const FAVORITES_STORAGE_KEY = 'shortdrama_favorites';

// 用户认证管理类
class AuthManager {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.users = this.getUsers();
        this.initializeDefaultUsers();
    }

    // 初始化默认用户（用于演示）
    initializeDefaultUsers() {
        if (this.users.length === 0) {
            const defaultUsers = [
                {
                    id: 1,
                    username: 'demo_user',
                    email: '<EMAIL>',
                    password: this.hashPassword('123456'),
                    phone: '13800138000',
                    avatar: 'https://via.placeholder.com/120x120/667eea/ffffff?text=Demo',
                    bio: '这是一个演示账户',
                    birthday: '1990-01-01',
                    joinDate: '2024-01-01',
                    settings: {
                        recordHistory: true,
                        personalizedRecommend: true
                    }
                }
            ];
            this.saveUsers(defaultUsers);
            this.users = defaultUsers;
        }
    }

    // 简单的密码哈希（实际项目中应使用更安全的方法）
    hashPassword(password) {
        return btoa(password + 'shortdrama_salt');
    }

    // 验证密码
    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }

    // 获取当前用户
    getCurrentUser() {
        const userData = localStorage.getItem(USER_STORAGE_KEY);
        return userData ? JSON.parse(userData) : null;
    }

    // 获取所有用户
    getUsers() {
        const usersData = localStorage.getItem(USERS_STORAGE_KEY);
        return usersData ? JSON.parse(usersData) : [];
    }

    // 保存用户数据
    saveUsers(users) {
        localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(users));
    }

    // 保存当前用户
    saveCurrentUser(user) {
        localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
        this.currentUser = user;
    }

    // 用户注册
    register(userData) {
        const { username, email, password, phone } = userData;

        // 验证用户名和邮箱是否已存在
        const existingUser = this.users.find(user => 
            user.username === username || user.email === email
        );

        if (existingUser) {
            if (existingUser.username === username) {
                throw new Error('用户名已存在');
            }
            if (existingUser.email === email) {
                throw new Error('邮箱已被注册');
            }
        }

        // 创建新用户
        const newUser = {
            id: Date.now(),
            username,
            email,
            password: this.hashPassword(password),
            phone: phone || '',
            avatar: `https://via.placeholder.com/120x120/667eea/ffffff?text=${encodeURIComponent(username.charAt(0).toUpperCase())}`,
            bio: '',
            birthday: '',
            joinDate: new Date().toISOString().split('T')[0],
            settings: {
                recordHistory: true,
                personalizedRecommend: true
            }
        };

        // 添加到用户列表
        this.users.push(newUser);
        this.saveUsers(this.users);

        // 自动登录
        const userForLogin = { ...newUser };
        delete userForLogin.password; // 不在客户端存储密码
        this.saveCurrentUser(userForLogin);

        return userForLogin;
    }

    // 用户登录
    login(email, password, rememberMe = false) {
        const user = this.users.find(u => u.email === email);
        
        if (!user) {
            throw new Error('用户不存在');
        }

        if (!this.verifyPassword(password, user.password)) {
            throw new Error('密码错误');
        }

        // 登录成功，保存用户信息（不包含密码）
        const userForLogin = { ...user };
        delete userForLogin.password;
        
        this.saveCurrentUser(userForLogin);

        // 如果选择记住我，设置更长的过期时间（这里简化处理）
        if (rememberMe) {
            // 实际项目中可以设置token过期时间
            userForLogin.rememberMe = true;
            this.saveCurrentUser(userForLogin);
        }

        return userForLogin;
    }

    // 用户登出
    logout() {
        localStorage.removeItem(USER_STORAGE_KEY);
        this.currentUser = null;
        // 跳转到首页
        window.location.href = 'index.html';
    }

    // 更新用户信息
    updateProfile(updates) {
        if (!this.currentUser) {
            throw new Error('用户未登录');
        }

        // 更新当前用户信息
        const updatedUser = { ...this.currentUser, ...updates };
        this.saveCurrentUser(updatedUser);

        // 更新用户列表中的信息
        const userIndex = this.users.findIndex(u => u.id === this.currentUser.id);
        if (userIndex !== -1) {
            this.users[userIndex] = { ...this.users[userIndex], ...updates };
            this.saveUsers(this.users);
        }

        return updatedUser;
    }

    // 检查是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // 获取用户统计信息
    getUserStats() {
        if (!this.currentUser) return null;

        const history = this.getWatchHistory();
        const favorites = this.getFavorites();
        const joinDate = new Date(this.currentUser.joinDate);
        const now = new Date();
        const joinDays = Math.ceil((now - joinDate) / (1000 * 60 * 60 * 24));

        return {
            watchedCount: history.length,
            favoriteCount: favorites.length,
            joinDays: joinDays
        };
    }

    // 获取观看历史
    getWatchHistory() {
        if (!this.currentUser) return [];
        const historyData = localStorage.getItem(`${HISTORY_STORAGE_KEY}_${this.currentUser.id}`);
        return historyData ? JSON.parse(historyData) : [];
    }

    // 添加观看历史
    addToHistory(video, progress = 0) {
        if (!this.currentUser || !this.currentUser.settings.recordHistory) return;

        const history = this.getWatchHistory();
        const existingIndex = history.findIndex(item => item.videoId === video.id);

        const historyItem = {
            videoId: video.id,
            title: video.title,
            thumbnail: video.thumbnail,
            duration: video.duration,
            category: video.category,
            progress: progress,
            watchTime: new Date().toISOString(),
            completed: progress >= 90 // 观看进度超过90%认为已完成
        };

        if (existingIndex !== -1) {
            // 更新现有记录
            history[existingIndex] = historyItem;
        } else {
            // 添加新记录，保持最新的在前面
            history.unshift(historyItem);
        }

        // 限制历史记录数量（最多保存100条）
        if (history.length > 100) {
            history.splice(100);
        }

        localStorage.setItem(`${HISTORY_STORAGE_KEY}_${this.currentUser.id}`, JSON.stringify(history));
    }

    // 清空观看历史
    clearHistory() {
        if (!this.currentUser) return;
        localStorage.removeItem(`${HISTORY_STORAGE_KEY}_${this.currentUser.id}`);
    }

    // 获取收藏列表
    getFavorites() {
        if (!this.currentUser) return [];
        const favoritesData = localStorage.getItem(`${FAVORITES_STORAGE_KEY}_${this.currentUser.id}`);
        return favoritesData ? JSON.parse(favoritesData) : [];
    }

    // 添加到收藏
    addToFavorites(video) {
        if (!this.currentUser) return false;

        const favorites = this.getFavorites();
        const existingIndex = favorites.findIndex(item => item.videoId === video.id);

        if (existingIndex !== -1) {
            return false; // 已经收藏过了
        }

        const favoriteItem = {
            videoId: video.id,
            title: video.title,
            thumbnail: video.thumbnail,
            duration: video.duration,
            category: video.category,
            addTime: new Date().toISOString()
        };

        favorites.unshift(favoriteItem);
        localStorage.setItem(`${FAVORITES_STORAGE_KEY}_${this.currentUser.id}`, JSON.stringify(favorites));
        return true;
    }

    // 从收藏中移除
    removeFromFavorites(videoId) {
        if (!this.currentUser) return false;

        const favorites = this.getFavorites();
        const filteredFavorites = favorites.filter(item => item.videoId !== videoId);

        if (filteredFavorites.length === favorites.length) {
            return false; // 没有找到要移除的项目
        }

        localStorage.setItem(`${FAVORITES_STORAGE_KEY}_${this.currentUser.id}`, JSON.stringify(filteredFavorites));
        return true;
    }

    // 检查是否已收藏
    isFavorited(videoId) {
        if (!this.currentUser) return false;
        const favorites = this.getFavorites();
        return favorites.some(item => item.videoId === videoId);
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 消息提示功能
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    if (!toast) return;

    const icon = toast.querySelector('.toast-icon');
    const messageEl = toast.querySelector('.toast-message');

    // 设置图标
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    icon.className = `toast-icon ${icons[type] || icons.success}`;
    messageEl.textContent = message;
    
    // 设置样式类
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    // 3秒后自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 更新导航栏用户信息
function updateNavUser() {
    const navUser = document.getElementById('navUser');
    if (!navUser) return;

    if (authManager.isLoggedIn()) {
        const user = authManager.currentUser;
        navUser.innerHTML = `
            <div class="user-dropdown">
                <button class="user-btn" id="userMenuBtn">
                    <img src="${user.avatar}" alt="${user.username}" class="user-avatar">
                    <span class="user-name">${user.username}</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="user-menu" id="userMenu">
                    <a href="profile.html" class="menu-item">
                        <i class="fas fa-user"></i> 个人中心
                    </a>
                    <a href="profile.html?tab=favorites" class="menu-item">
                        <i class="fas fa-heart"></i> 我的收藏
                    </a>
                    <a href="profile.html?tab=history" class="menu-item">
                        <i class="fas fa-history"></i> 观看历史
                    </a>
                    <div class="menu-divider"></div>
                    <button class="menu-item logout-btn" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </button>
                </div>
            </div>
        `;

        // 绑定用户菜单事件
        bindUserMenuEvents();
    } else {
        navUser.innerHTML = `
            <div class="nav-auth">
                <a href="login.html" class="auth-link">登录</a>
                <a href="register.html" class="auth-link primary">注册</a>
            </div>
        `;
    }
}

// 绑定用户菜单事件
function bindUserMenuEvents() {
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');
    const logoutBtn = document.getElementById('logoutBtn');

    if (userMenuBtn && userMenu) {
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenu.classList.toggle('show');
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            userMenu.classList.remove('show');
        });

        userMenu.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            if (confirm('确定要退出登录吗？')) {
                authManager.logout();
            }
        });
    }
}

// 页面加载时更新导航栏
document.addEventListener('DOMContentLoaded', () => {
    updateNavUser();
});

// 导出全局对象
window.authManager = authManager;
window.showToast = showToast;
window.updateNavUser = updateNavUser;
