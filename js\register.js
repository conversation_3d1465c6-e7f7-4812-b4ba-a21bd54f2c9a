// 注册页面JavaScript

// DOM元素
const registerForm = document.getElementById('registerForm');
const usernameInput = document.getElementById('username');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirmPassword');
const phoneInput = document.getElementById('phone');
const agreeTermsInput = document.getElementById('agreeTerms');
const togglePasswordBtn = document.getElementById('togglePassword');
const registerBtn = document.getElementById('registerBtn');
const passwordStrength = document.getElementById('passwordStrength');
const navToggle = document.getElementById('navToggle');

// 状态变量
let isLoading = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果已经登录，跳转到首页
    if (authManager.isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    bindEvents();
    setupFormValidation();
});

// 绑定事件
function bindEvents() {
    // 表单提交
    registerForm.addEventListener('submit', handleRegister);
    
    // 密码显示/隐藏切换
    togglePasswordBtn.addEventListener('click', togglePasswordVisibility);
    
    // 输入框实时验证
    usernameInput.addEventListener('input', validateUsername);
    emailInput.addEventListener('input', validateEmail);
    passwordInput.addEventListener('input', () => {
        validatePassword();
        updatePasswordStrength();
    });
    confirmPasswordInput.addEventListener('input', validateConfirmPassword);
    phoneInput.addEventListener('input', validatePhone);
    
    // 移动端导航
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // 社交注册按钮
    document.querySelectorAll('.social-btn').forEach(btn => {
        btn.addEventListener('click', handleSocialRegister);
    });
    
    // 用户协议链接
    document.querySelectorAll('.terms-link').forEach(link => {
        link.addEventListener('click', handleTermsClick);
    });
}

// 处理注册
async function handleRegister(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    const formData = {
        username: usernameInput.value.trim(),
        email: emailInput.value.trim(),
        password: passwordInput.value,
        confirmPassword: confirmPasswordInput.value,
        phone: phoneInput.value.trim(),
        agreeTerms: agreeTermsInput.checked
    };
    
    // 验证表单
    if (!validateForm(formData)) {
        return;
    }
    
    setLoading(true);
    
    try {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 执行注册
        const user = authManager.register(formData);
        
        showToast('注册成功！欢迎加入短剧TV', 'success');
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
        
    } catch (error) {
        showToast(error.message, 'error');
        setLoading(false);
    }
}

// 设置加载状态
function setLoading(loading) {
    isLoading = loading;
    registerBtn.disabled = loading;
    
    const btnText = registerBtn.querySelector('.btn-text');
    const btnLoading = registerBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'flex';
        registerBtn.classList.add('loading');
    } else {
        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
        registerBtn.classList.remove('loading');
    }
}

// 切换密码可见性
function togglePasswordVisibility() {
    const type = passwordInput.type === 'password' ? 'text' : 'password';
    passwordInput.type = type;
    confirmPasswordInput.type = type;
    
    const icon = togglePasswordBtn.querySelector('i');
    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
}

// 更新密码强度指示器
function updatePasswordStrength() {
    const password = passwordInput.value;
    const strengthFill = passwordStrength.querySelector('.strength-fill');
    const strengthText = passwordStrength.querySelector('.strength-text');
    
    if (!password) {
        strengthFill.style.width = '0%';
        strengthFill.className = 'strength-fill';
        strengthText.textContent = '密码强度';
        return;
    }
    
    let score = 0;
    let feedback = '';
    
    // 长度检查
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // 字符类型检查
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    // 设置强度等级
    if (score <= 2) {
        strengthFill.className = 'strength-fill weak';
        feedback = '弱';
    } else if (score <= 4) {
        strengthFill.className = 'strength-fill medium';
        feedback = '中等';
    } else {
        strengthFill.className = 'strength-fill strong';
        feedback = '强';
    }
    
    strengthText.textContent = `密码强度: ${feedback}`;
}

// 表单验证
function validateForm(formData) {
    let isValid = true;
    
    // 验证用户名
    if (!validateUsername()) isValid = false;
    
    // 验证邮箱
    if (!validateEmail()) isValid = false;
    
    // 验证密码
    if (!validatePassword()) isValid = false;
    
    // 验证确认密码
    if (!validateConfirmPassword()) isValid = false;
    
    // 验证手机号（可选）
    if (formData.phone && !validatePhone()) isValid = false;
    
    // 验证用户协议
    if (!formData.agreeTerms) {
        showToast('请阅读并同意用户协议和隐私政策', 'warning');
        isValid = false;
    }
    
    return isValid;
}

// 验证用户名
function validateUsername() {
    const username = usernameInput.value.trim();
    
    removeFieldError(usernameInput);
    
    if (!username) {
        showFieldError(usernameInput, '请输入用户名');
        return false;
    }
    
    if (username.length < 3 || username.length > 20) {
        showFieldError(usernameInput, '用户名长度为3-20个字符');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
        showFieldError(usernameInput, '用户名只能包含字母、数字、下划线和中文');
        return false;
    }
    
    return true;
}

// 验证邮箱
function validateEmail() {
    const email = emailInput.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    removeFieldError(emailInput);
    
    if (!email) {
        showFieldError(emailInput, '请输入邮箱地址');
        return false;
    }
    
    if (!emailRegex.test(email)) {
        showFieldError(emailInput, '请输入有效的邮箱地址');
        return false;
    }
    
    return true;
}

// 验证密码
function validatePassword() {
    const password = passwordInput.value;
    
    removeFieldError(passwordInput);
    
    if (!password) {
        showFieldError(passwordInput, '请输入密码');
        return false;
    }
    
    if (password.length < 8) {
        showFieldError(passwordInput, '密码长度至少8位');
        return false;
    }
    
    if (!/(?=.*[a-zA-Z])(?=.*[0-9])/.test(password)) {
        showFieldError(passwordInput, '密码必须包含字母和数字');
        return false;
    }
    
    return true;
}

// 验证确认密码
function validateConfirmPassword() {
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    
    removeFieldError(confirmPasswordInput);
    
    if (!confirmPassword) {
        showFieldError(confirmPasswordInput, '请确认密码');
        return false;
    }
    
    if (password !== confirmPassword) {
        showFieldError(confirmPasswordInput, '两次输入的密码不一致');
        return false;
    }
    
    return true;
}

// 验证手机号
function validatePhone() {
    const phone = phoneInput.value.trim();
    const phoneRegex = /^1[3-9]\d{9}$/;
    
    removeFieldError(phoneInput);
    
    if (phone && !phoneRegex.test(phone)) {
        showFieldError(phoneInput, '请输入有效的手机号码');
        return false;
    }
    
    return true;
}

// 显示字段错误
function showFieldError(input, message) {
    const wrapper = input.closest('.input-wrapper');
    const formGroup = input.closest('.form-group');
    
    // 移除现有错误
    removeFieldError(input);
    
    // 添加错误样式
    wrapper.classList.add('error');
    
    // 添加错误消息
    const errorEl = document.createElement('div');
    errorEl.className = 'field-error';
    errorEl.textContent = message;
    formGroup.appendChild(errorEl);
}

// 移除字段错误
function removeFieldError(input) {
    const wrapper = input.closest('.input-wrapper');
    const formGroup = input.closest('.form-group');
    
    wrapper.classList.remove('error');
    
    const existingError = formGroup.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// 设置表单验证样式
function setupFormValidation() {
    // 添加错误样式到CSS
    const style = document.createElement('style');
    style.textContent = `
        .input-wrapper.error input {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .field-error {
            color: #dc3545;
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }
    `;
    document.head.appendChild(style);
}

// 处理社交注册
function handleSocialRegister(e) {
    const platform = e.currentTarget.classList.contains('wechat') ? '微信' : 'QQ';
    showToast(`${platform}注册功能暂未开放`, 'info');
}

// 处理用户协议点击
function handleTermsClick(e) {
    e.preventDefault();
    showToast('用户协议和隐私政策页面暂未开放', 'info');
}

// 键盘事件处理
document.addEventListener('keydown', (e) => {
    // Enter键提交表单
    if (e.key === 'Enter' && !isLoading) {
        const activeElement = document.activeElement;
        if ([usernameInput, emailInput, passwordInput, confirmPasswordInput, phoneInput].includes(activeElement)) {
            handleRegister(e);
        }
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，检查登录状态
        if (authManager.isLoggedIn()) {
            window.location.href = 'index.html';
        }
    }
});
