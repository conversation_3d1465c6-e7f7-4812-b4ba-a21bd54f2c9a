// 数据加载器 - 从JSON文件加载视频数据

// 全局数据变量
let videoData = [];
let commentsData = {};
let hotKeywords = [];
let categoryInfo = {};

// 数据加载状态
let dataLoaded = false;
let loadingPromise = null;

// 加载JSON数据
async function loadVideoData() {
    if (dataLoaded) {
        return Promise.resolve();
    }
    
    if (loadingPromise) {
        return loadingPromise;
    }
    
    loadingPromise = new Promise(async (resolve, reject) => {
        try {
            showLoadingIndicator();
            
            const response = await fetch('data/videos.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 更新全局变量
            videoData = data.videos || [];
            commentsData = data.comments || {};
            hotKeywords = data.hotKeywords || [];
            categoryInfo = data.categoryInfo || {};
            
            // 标记数据已加载
            dataLoaded = true;
            
            // 触发数据加载完成事件
            document.dispatchEvent(new CustomEvent('dataLoaded', {
                detail: { videoData, commentsData, hotKeywords, categoryInfo }
            }));
            
            hideLoadingIndicator();
            resolve();
            
        } catch (error) {
            console.error('加载视频数据失败:', error);
            hideLoadingIndicator();
            showErrorMessage('数据加载失败，请刷新页面重试');
            reject(error);
        }
    });
    
    return loadingPromise;
}

// 显示加载指示器
function showLoadingIndicator() {
    // 检查是否已存在加载指示器
    let loader = document.getElementById('globalLoader');
    if (!loader) {
        loader = document.createElement('div');
        loader.id = 'globalLoader';
        loader.className = 'global-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-spinner"></div>
                <p>加载中...</p>
            </div>
        `;
        document.body.appendChild(loader);
        
        // 添加样式
        addLoaderStyles();
    }
    
    loader.style.display = 'flex';
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    const loader = document.getElementById('globalLoader');
    if (loader) {
        loader.style.display = 'none';
    }
}

// 显示错误消息
function showErrorMessage(message) {
    // 检查是否已存在错误提示
    let errorDiv = document.getElementById('globalError');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.id = 'globalError';
        errorDiv.className = 'global-error';
        document.body.appendChild(errorDiv);
        
        // 添加样式
        addErrorStyles();
    }
    
    errorDiv.innerHTML = `
        <div class="error-content">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>加载失败</h3>
            <p>${message}</p>
            <button class="retry-btn" onclick="retryLoadData()">
                <i class="fas fa-redo"></i> 重试
            </button>
        </div>
    `;
    
    errorDiv.style.display = 'flex';
}

// 隐藏错误消息
function hideErrorMessage() {
    const errorDiv = document.getElementById('globalError');
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
}

// 重试加载数据
function retryLoadData() {
    hideErrorMessage();
    dataLoaded = false;
    loadingPromise = null;
    loadVideoData();
}

// 添加加载器样式
function addLoaderStyles() {
    if (document.getElementById('loaderStyles')) return;
    
    const style = document.createElement('style');
    style.id = 'loaderStyles';
    style.textContent = `
        .global-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }
        
        .loader-content {
            text-align: center;
            color: #333;
        }
        
        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loader-content p {
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
    `;
    document.head.appendChild(style);
}

// 添加错误样式
function addErrorStyles() {
    if (document.getElementById('errorStyles')) return;
    
    const style = document.createElement('style');
    style.id = 'errorStyles';
    style.textContent = `
        .global-error {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .error-content {
            background: white;
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            max-width: 400px;
            margin: 0 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .error-icon {
            font-size: 3rem;
            color: #ff6b6b;
            margin-bottom: 1rem;
        }
        
        .error-content h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .error-content p {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.5;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
}

// 获取视频数据的便捷方法
function getVideoData() {
    return videoData;
}

function getCommentsData() {
    return commentsData;
}

function getHotKeywords() {
    return hotKeywords;
}

function getCategoryInfo() {
    return categoryInfo;
}

// 根据ID获取视频
function getVideoById(id) {
    return videoData.find(video => video.id === parseInt(id));
}

// 根据分类获取视频
function getVideosByCategory(category) {
    if (category === 'all' || !category) {
        return videoData;
    }
    return videoData.filter(video => video.category === category);
}

// 搜索视频
function searchVideos(keyword, category = '', sortBy = 'relevance') {
    let results = videoData.filter(video => {
        const matchKeyword = !keyword || 
            video.title.toLowerCase().includes(keyword.toLowerCase()) ||
            video.description.toLowerCase().includes(keyword.toLowerCase()) ||
            video.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()));
        
        const matchCategory = !category || video.category === category;
        
        return matchKeyword && matchCategory;
    });
    
    // 排序
    switch (sortBy) {
        case 'latest':
            results.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
            break;
        case 'popular':
            results.sort((a, b) => b.likes - a.likes);
            break;
        case 'views':
            results.sort((a, b) => {
                const aViews = parseInt(a.views.replace(/[^\d]/g, ''));
                const bViews = parseInt(b.views.replace(/[^\d]/g, ''));
                return bViews - aViews;
            });
            break;
        case 'rating':
            results.sort((a, b) => b.rating - a.rating);
            break;
        default: // relevance
            if (keyword) {
                results.sort((a, b) => {
                    const aInTitle = a.title.toLowerCase().includes(keyword.toLowerCase());
                    const bInTitle = b.title.toLowerCase().includes(keyword.toLowerCase());
                    if (aInTitle && !bInTitle) return -1;
                    if (!aInTitle && bInTitle) return 1;
                    return b.likes - a.likes;
                });
            }
            break;
    }
    
    return results;
}

// 页面加载时自动加载数据
document.addEventListener('DOMContentLoaded', function() {
    loadVideoData().catch(error => {
        console.error('初始数据加载失败:', error);
    });
});

// 导出函数供其他脚本使用
window.loadVideoData = loadVideoData;
window.getVideoData = getVideoData;
window.getCommentsData = getCommentsData;
window.getHotKeywords = getHotKeywords;
window.getCategoryInfo = getCategoryInfo;
window.getVideoById = getVideoById;
window.getVideosByCategory = getVideosByCategory;
window.searchVideos = searchVideos;
