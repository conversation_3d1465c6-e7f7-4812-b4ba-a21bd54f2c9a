<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="#" class="nav-link">热门</a>
                <a href="list.html" class="nav-link">分类</a>
                <a href="#" class="nav-link">收藏</a>
            </div>
            <div class="nav-search">
                <input type="text" placeholder="搜索短剧..." id="searchInput">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
            </div>
            <div class="nav-user" id="navUser">
                <!-- 用户信息将通过JavaScript动态生成 -->
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 面包屑导航 -->
        <section class="breadcrumb-section">
            <div class="container">
                <nav class="breadcrumb">
                    <a href="index.html"><i class="fas fa-home"></i> 首页</a>
                    <span class="separator">/</span>
                    <span class="current">搜索结果</span>
                </nav>
            </div>
        </section>

        <!-- 搜索结果标题 -->
        <section class="search-header">
            <div class="container">
                <div class="search-info">
                    <h1 class="search-title">
                        搜索结果: "<span id="searchKeyword">关键词</span>"
                    </h1>
                    <p class="search-stats">
                        找到 <span id="resultCount">0</span> 个相关结果
                        <span class="search-time">（用时 <span id="searchTime">0.01</span> 秒）</span>
                    </p>
                </div>
                
                <!-- 高级搜索 -->
                <div class="advanced-search" id="advancedSearch">
                    <div class="search-form">
                        <div class="search-row">
                            <div class="search-field">
                                <label>关键词:</label>
                                <input type="text" id="keywordInput" placeholder="输入搜索关键词">
                            </div>
                            <div class="search-field">
                                <label>分类:</label>
                                <select id="categorySelect">
                                    <option value="">全部分类</option>
                                    <option value="romance">言情</option>
                                    <option value="comedy">喜剧</option>
                                    <option value="drama">剧情</option>
                                    <option value="action">动作</option>
                                    <option value="suspense">悬疑</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-row">
                            <div class="search-field">
                                <label>时长:</label>
                                <select id="durationSelect">
                                    <option value="">不限</option>
                                    <option value="short">10分钟以下</option>
                                    <option value="medium">10-20分钟</option>
                                    <option value="long">20分钟以上</option>
                                </select>
                            </div>
                            <div class="search-field">
                                <label>发布时间:</label>
                                <select id="dateSelect">
                                    <option value="">不限</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                    <option value="year">今年</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="search-btn" id="advancedSearchBtn">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button class="reset-btn" id="resetBtn">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <button class="toggle-advanced" id="toggleAdvanced">
                    <i class="fas fa-sliders-h"></i> 高级搜索
                </button>
            </div>
        </section>

        <!-- 搜索建议 -->
        <section class="search-suggestions" id="searchSuggestions" style="display: none;">
            <div class="container">
                <div class="suggestions-content">
                    <h3>您是否要找:</h3>
                    <div class="suggestions-list" id="suggestionsList">
                        <!-- 搜索建议将通过JavaScript生成 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选和排序 -->
        <section class="filter-section">
            <div class="container">
                <div class="filter-controls">
                    <div class="filter-tags" id="filterTags">
                        <!-- 当前筛选条件标签 -->
                    </div>
                    <div class="sort-controls">
                        <label>排序:</label>
                        <select id="sortSelect" class="sort-select">
                            <option value="relevance">相关度</option>
                            <option value="latest">最新发布</option>
                            <option value="popular">最受欢迎</option>
                            <option value="views">播放量</option>
                            <option value="rating">评分</option>
                        </select>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid" title="网格视图">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜索结果 -->
        <section class="results-section">
            <div class="container">
                <!-- 加载状态 -->
                <div class="loading" id="loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>搜索中...</p>
                </div>
                
                <!-- 搜索结果列表 -->
                <div class="results-grid" id="resultsGrid">
                    <!-- 搜索结果将通过JavaScript生成 -->
                </div>
                
                <!-- 无结果状态 -->
                <div class="no-results" id="noResults" style="display: none;">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>未找到相关结果</h3>
                    <p>尝试使用不同的关键词或调整搜索条件</p>
                    <div class="search-tips">
                        <h4>搜索建议:</h4>
                        <ul>
                            <li>检查关键词拼写是否正确</li>
                            <li>尝试使用更通用的关键词</li>
                            <li>减少搜索条件的限制</li>
                            <li>使用同义词或相关词汇</li>
                        </ul>
                    </div>
                    <button class="back-btn" onclick="window.location.href='index.html'">
                        返回首页
                    </button>
                </div>
                
                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <button class="page-btn prev-page" id="prevPage" disabled>
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <div class="page-numbers" id="pageNumbers">
                        <!-- 页码将通过JavaScript生成 -->
                    </div>
                    <button class="page-btn next-page" id="nextPage">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- 热门搜索 -->
        <section class="hot-searches">
            <div class="container">
                <h3>热门搜索</h3>
                <div class="hot-keywords" id="hotKeywords">
                    <!-- 热门关键词将通过JavaScript生成 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <p>专业的短剧视频平台，为您提供优质的短剧内容。</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>邮箱: <EMAIL></p>
                    <p>电话: 400-123-4567</p>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-wechat"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 短剧TV. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/search.js"></script>
</body>
</html>
