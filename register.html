<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="list.html?category=all" class="nav-link">热门</a>
                <a href="list.html" class="nav-link">分类</a>
            </div>
            <div class="nav-auth">
                <a href="login.html" class="auth-link">登录</a>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>加入短剧TV</h1>
                    <p>创建账户，开启您的短剧之旅</p>
                </div>
                
                <form class="auth-form" id="registerForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user"></i>
                            <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                        </div>
                        <div class="field-hint">用户名长度为3-20个字符</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="请输入密码" required>
                            <button type="button" class="toggle-password" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">密码强度</span>
                        </div>
                        <div class="field-hint">密码长度至少8位，包含字母和数字</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">手机号码（可选）</label>
                        <div class="input-wrapper">
                            <i class="fas fa-phone"></i>
                            <input type="tel" id="phone" name="phone" placeholder="请输入手机号码">
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                            <span class="checkmark"></span>
                            我已阅读并同意 <a href="#" class="terms-link">用户协议</a> 和 <a href="#" class="terms-link">隐私政策</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="auth-btn primary" id="registerBtn">
                        <span class="btn-text">注册账户</span>
                        <div class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                    
                    <div class="auth-divider">
                        <span>或者</span>
                    </div>
                    
                    <div class="social-login">
                        <button type="button" class="social-btn wechat">
                            <i class="fab fa-weixin"></i>
                            微信注册
                        </button>
                        <button type="button" class="social-btn qq">
                            <i class="fab fa-qq"></i>
                            QQ注册
                        </button>
                    </div>
                </form>
                
                <div class="auth-footer">
                    <p>已有账户？ <a href="login.html">立即登录</a></p>
                </div>
            </div>
            
            <!-- 注册背景装饰 -->
            <div class="auth-decoration">
                <div class="decoration-item item-1"></div>
                <div class="decoration-item item-2"></div>
                <div class="decoration-item item-3"></div>
            </div>
        </div>
    </main>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/register.js"></script>
</body>
</html>
