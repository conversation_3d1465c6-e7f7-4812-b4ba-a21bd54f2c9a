// 共享数据文件

// 扩展的视频数据
const videoData = [
    {
        id: 1,
        title: "霸道总裁的小娇妻",
        category: "romance",
        thumbnail: "https://via.placeholder.com/300x200/ff6b6b/ffffff?text=霸道总裁",
        duration: "15:30",
        views: "128万",
        likes: 8520,
        publishDate: "2024-06-25",
        rating: 4.8,
        description: "一个关于霸道总裁和小娇妻的浪漫爱情故事。女主角意外闯入总裁的生活，从此开始了一段甜蜜而又充满挑战的恋爱之旅。剧情跌宕起伏，情感真挚动人，是一部不可错过的言情佳作。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["言情", "霸道总裁", "甜宠", "现代"],
        cast: ["张三", "李四"],
        director: "王导演"
    },
    {
        id: 2,
        title: "搞笑日常生活",
        category: "comedy",
        thumbnail: "https://via.placeholder.com/300x200/2ecc71/ffffff?text=搞笑日常",
        duration: "12:45",
        views: "89万",
        likes: 6340,
        publishDate: "2024-06-24",
        rating: 4.5,
        description: "轻松搞笑的日常生活片段，让你开怀大笑。通过幽默诙谐的方式展现普通人的生活趣事，每一个情节都充满了意想不到的笑点，是缓解压力的最佳选择。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["喜剧", "日常", "搞笑", "轻松"],
        cast: ["赵五", "钱六"],
        director: "李导演"
    },
    {
        id: 3,
        title: "悬疑推理案件",
        category: "suspense",
        thumbnail: "https://via.placeholder.com/300x200/9b59b6/ffffff?text=悬疑推理",
        duration: "20:15",
        views: "156万",
        likes: 9870,
        publishDate: "2024-06-23",
        rating: 4.9,
        description: "扣人心弦的悬疑推理故事，真相只有一个。一起看似简单的案件背后隐藏着惊天秘密，主人公通过缜密的推理和调查，一步步揭开真相的面纱。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["悬疑", "推理", "烧脑", "犯罪"],
        cast: ["孙七", "周八"],
        director: "陈导演"
    },
    {
        id: 4,
        title: "动作冒险传奇",
        category: "action",
        thumbnail: "https://via.placeholder.com/300x200/e74c3c/ffffff?text=动作冒险",
        duration: "18:20",
        views: "203万",
        likes: 12450,
        publishDate: "2024-06-22",
        rating: 4.7,
        description: "激动人心的动作冒险故事，英雄的传奇之路。主人公在危机四伏的世界中闯荡，凭借过人的武艺和智慧化解重重危机，最终成就一代传奇。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["动作", "冒险", "英雄", "传奇"],
        cast: ["吴九", "郑十"],
        director: "刘导演"
    },
    {
        id: 5,
        title: "温情家庭剧",
        category: "drama",
        thumbnail: "https://via.placeholder.com/300x200/f39c12/ffffff?text=家庭剧",
        duration: "22:10",
        views: "95万",
        likes: 5680,
        publishDate: "2024-06-21",
        rating: 4.6,
        description: "温暖人心的家庭故事，亲情的力量无穷。讲述了一个普通家庭在面对生活困难时相互扶持、共同成长的感人故事，展现了家庭的温暖和亲情的珍贵。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["剧情", "家庭", "温情", "亲情"],
        cast: ["冯十一", "陈十二"],
        director: "黄导演"
    },
    {
        id: 6,
        title: "校园青春恋曲",
        category: "romance",
        thumbnail: "https://via.placeholder.com/300x200/3498db/ffffff?text=校园青春",
        duration: "16:40",
        views: "167万",
        likes: 10230,
        publishDate: "2024-06-20",
        rating: 4.8,
        description: "青春校园的美好恋爱故事，回忆那些年的美好。男女主角在校园中相遇相知相爱，经历了青春的懵懂和成长的烦恼，最终收获了美好的爱情。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["言情", "校园", "青春", "恋爱"],
        cast: ["褚十三", "卫十四"],
        director: "杨导演"
    },
    {
        id: 7,
        title: "爆笑喜剧合集",
        category: "comedy",
        thumbnail: "https://via.placeholder.com/300x200/1abc9c/ffffff?text=爆笑喜剧",
        duration: "14:25",
        views: "234万",
        likes: 15670,
        publishDate: "2024-06-19",
        rating: 4.9,
        description: "精选爆笑喜剧片段，保证让你笑到肚子疼。汇集了各种经典搞笑桥段，从日常生活到奇思妙想，每一个片段都是精心挑选的笑料。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["喜剧", "爆笑", "合集", "经典"],
        cast: ["蒋十五", "沈十六"],
        director: "何导演"
    },
    {
        id: 8,
        title: "古装武侠传说",
        category: "action",
        thumbnail: "https://via.placeholder.com/300x200/8e44ad/ffffff?text=古装武侠",
        duration: "25:30",
        views: "189万",
        likes: 11340,
        publishDate: "2024-06-18",
        rating: 4.7,
        description: "江湖恩怨情仇，武侠世界的传奇故事。在刀光剑影的江湖中，主人公凭借高超的武艺和正义的信念，行侠仗义，最终成为一代武林宗师。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["动作", "武侠", "古装", "江湖"],
        cast: ["韩十七", "杨十八"],
        director: "朱导演"
    },
    {
        id: 9,
        title: "都市情感纠葛",
        category: "drama",
        thumbnail: "https://via.placeholder.com/300x200/e67e22/ffffff?text=都市情感",
        duration: "19:45",
        views: "142万",
        likes: 8760,
        publishDate: "2024-06-17",
        rating: 4.5,
        description: "现代都市中的复杂情感关系，展现了都市人的爱恨情仇。通过几个主要角色的情感纠葛，深刻反映了现代社会中人与人之间的复杂关系。",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["剧情", "都市", "情感", "现代"],
        cast: ["秦十九", "尤二十"],
        director: "许导演"
    },
    {
        id: 10,
        title: "科幻未来世界",
        category: "action",
        thumbnail: "https://via.placeholder.com/300x200/34495e/ffffff?text=科幻未来",
        duration: "21:30",
        views: "176万",
        likes: 9540,
        publishDate: "2024-06-16",
        rating: 4.6,
        description: "充满想象力的科幻故事，探索未来世界的可能性。在高科技的未来社会中，人类面临着前所未有的挑战和机遇，主人公将如何应对这个全新的世界？",
        videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
        tags: ["动作", "科幻", "未来", "高科技"],
        cast: ["许二一", "何二二"],
        director: "吕导演"
    }
];

// 评论数据
const commentsData = {
    1: [
        {
            id: 1,
            author: "爱看剧的小仙女",
            avatar: "A",
            content: "太好看了！男主角真的很帅，女主角也很可爱，剧情发展很自然，没有那种很突兀的感觉。",
            time: "2小时前",
            likes: 23,
            replies: []
        },
        {
            id: 2,
            author: "剧迷老王",
            avatar: "王",
            content: "这种霸道总裁的剧情虽然老套，但是拍得很用心，演员的演技也在线，值得一看。",
            time: "5小时前",
            likes: 15,
            replies: []
        }
    ]
};

// 热门搜索关键词
const hotKeywords = [
    { keyword: "霸道总裁", trending: true },
    { keyword: "校园恋爱", trending: false },
    { keyword: "悬疑推理", trending: true },
    { keyword: "搞笑喜剧", trending: false },
    { keyword: "古装武侠", trending: false },
    { keyword: "都市情感", trending: true },
    { keyword: "科幻未来", trending: false },
    { keyword: "家庭温情", trending: false }
];

// 分类信息
const categoryInfo = {
    all: { name: "全部", description: "所有精彩短剧" },
    romance: { name: "言情", description: "浪漫爱情故事" },
    comedy: { name: "喜剧", description: "轻松搞笑内容" },
    drama: { name: "剧情", description: "深度剧情作品" },
    action: { name: "动作", description: "刺激动作场面" },
    suspense: { name: "悬疑", description: "烧脑推理故事" }
};

// 工具函数
const utils = {
    // 获取分类中文名称
    getCategoryName(category) {
        return categoryInfo[category]?.name || '其他';
    },
    
    // 格式化数字
    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return num.toString();
    },
    
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // 获取相对时间
    getRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return '昨天';
        if (diffDays <= 7) return `${diffDays}天前`;
        if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`;
        return `${Math.ceil(diffDays / 30)}个月前`;
    },
    
    // 搜索视频
    searchVideos(keyword, category = '', sortBy = 'relevance') {
        let results = videoData.filter(video => {
            const matchKeyword = !keyword || 
                video.title.toLowerCase().includes(keyword.toLowerCase()) ||
                video.description.toLowerCase().includes(keyword.toLowerCase()) ||
                video.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()));
            
            const matchCategory = !category || video.category === category;
            
            return matchKeyword && matchCategory;
        });
        
        // 排序
        switch (sortBy) {
            case 'latest':
                results.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
                break;
            case 'popular':
                results.sort((a, b) => b.likes - a.likes);
                break;
            case 'views':
                results.sort((a, b) => parseInt(b.views) - parseInt(a.views));
                break;
            case 'rating':
                results.sort((a, b) => b.rating - a.rating);
                break;
            default: // relevance
                // 简单的相关度排序：标题匹配优先
                if (keyword) {
                    results.sort((a, b) => {
                        const aInTitle = a.title.toLowerCase().includes(keyword.toLowerCase());
                        const bInTitle = b.title.toLowerCase().includes(keyword.toLowerCase());
                        if (aInTitle && !bInTitle) return -1;
                        if (!aInTitle && bInTitle) return 1;
                        return b.likes - a.likes;
                    });
                }
                break;
        }
        
        return results;
    },
    
    // 获取相关视频
    getRelatedVideos(videoId, limit = 6) {
        const currentVideo = videoData.find(v => v.id === videoId);
        if (!currentVideo) return [];
        
        return videoData
            .filter(v => v.id !== videoId && v.category === currentVideo.category)
            .sort((a, b) => b.likes - a.likes)
            .slice(0, limit);
    },
    
    // 高亮搜索关键词
    highlightKeyword(text, keyword) {
        if (!keyword) return text;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    },

    // 创建视频卡片HTML
    createVideoCard(video, isListView = false, searchKeyword = '') {
        const title = searchKeyword ? this.highlightKeyword(video.title, searchKeyword) : video.title;
        const description = searchKeyword ? this.highlightKeyword(video.description, searchKeyword) : video.description;

        return `
            <div class="video-card" data-video-id="${video.id}">
                <div class="video-thumbnail" style="background-image: url('${video.thumbnail}')">
                    <div class="play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                <div class="video-info">
                    <h3 class="video-title">${title}</h3>
                    ${isListView ? `<div class="video-description">${description}</div>` : ''}
                    <div class="video-meta">
                        <span class="video-duration">${video.duration}</span>
                        <span class="video-views">${video.views}次观看</span>
                        <span class="video-category">${this.getCategoryName(video.category)}</span>
                        ${isListView ? `<div class="video-stats">
                            <span><i class="fas fa-thumbs-up"></i> ${this.formatNumber(video.likes)}</span>
                            <span><i class="fas fa-star"></i> ${video.rating}</span>
                            <span><i class="fas fa-calendar"></i> ${this.getRelativeTime(video.publishDate)}</span>
                        </div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }
};

// 导出数据和工具函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        videoData,
        commentsData,
        hotKeywords,
        categoryInfo,
        utils
    };
}
