// 共享数据文件 - 现在从JSON文件加载数据

// 数据变量（将从JSON文件加载）
let videoData = [];
let commentsData = {};
let hotKeywords = [];
let categoryInfo = {};

// 监听数据加载完成事件
document.addEventListener('dataLoaded', function(event) {
    const data = event.detail;
    videoData = data.videoData;
    commentsData = data.commentsData;
    hotKeywords = data.hotKeywords;
    categoryInfo = data.categoryInfo;
});

// 工具函数
const utils = {
    // 获取分类中文名称
    getCategoryName(category) {
        return categoryInfo[category]?.name || '其他';
    },
    
    // 格式化数字
    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return num.toString();
    },
    
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // 获取相对时间
    getRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return '昨天';
        if (diffDays <= 7) return `${diffDays}天前`;
        if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`;
        return `${Math.ceil(diffDays / 30)}个月前`;
    },
    
    // 搜索视频
    searchVideos(keyword, category = '', sortBy = 'relevance') {
        let results = videoData.filter(video => {
            const matchKeyword = !keyword || 
                video.title.toLowerCase().includes(keyword.toLowerCase()) ||
                video.description.toLowerCase().includes(keyword.toLowerCase()) ||
                video.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()));
            
            const matchCategory = !category || video.category === category;
            
            return matchKeyword && matchCategory;
        });
        
        // 排序
        switch (sortBy) {
            case 'latest':
                results.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
                break;
            case 'popular':
                results.sort((a, b) => b.likes - a.likes);
                break;
            case 'views':
                results.sort((a, b) => {
                    const aViews = parseInt(a.views.replace(/[^\d]/g, ''));
                    const bViews = parseInt(b.views.replace(/[^\d]/g, ''));
                    return bViews - aViews;
                });
                break;
            case 'rating':
                results.sort((a, b) => b.rating - a.rating);
                break;
            default: // relevance
                // 简单的相关度排序：标题匹配优先
                if (keyword) {
                    results.sort((a, b) => {
                        const aInTitle = a.title.toLowerCase().includes(keyword.toLowerCase());
                        const bInTitle = b.title.toLowerCase().includes(keyword.toLowerCase());
                        if (aInTitle && !bInTitle) return -1;
                        if (!aInTitle && bInTitle) return 1;
                        return b.likes - a.likes;
                    });
                }
                break;
        }
        
        return results;
    },
    
    // 获取相关视频
    getRelatedVideos(videoId, limit = 6) {
        const currentVideo = videoData.find(v => v.id === videoId);
        if (!currentVideo) return [];
        
        return videoData
            .filter(v => v.id !== videoId && v.category === currentVideo.category)
            .sort((a, b) => b.likes - a.likes)
            .slice(0, limit);
    },
    
    // 高亮搜索关键词
    highlightKeyword(text, keyword) {
        if (!keyword) return text;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    },
    
    // 创建视频卡片HTML
    createVideoCard(video, isListView = false, searchKeyword = '') {
        const title = searchKeyword ? this.highlightKeyword(video.title, searchKeyword) : video.title;
        const description = searchKeyword ? this.highlightKeyword(video.description, searchKeyword) : video.description;
        
        return `
            <div class="video-card" data-video-id="${video.id}">
                <div class="video-thumbnail" style="background-image: url('${video.thumbnail}')">
                    <div class="play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                <div class="video-info">
                    <h3 class="video-title">${title}</h3>
                    ${isListView ? `<div class="video-description">${description}</div>` : ''}
                    <div class="video-meta">
                        <span class="video-duration">${video.duration}</span>
                        <span class="video-views">${video.views}次观看</span>
                        <span class="video-category">${this.getCategoryName(video.category)}</span>
                        ${isListView ? `<div class="video-stats">
                            <span><i class="fas fa-thumbs-up"></i> ${this.formatNumber(video.likes)}</span>
                            <span><i class="fas fa-star"></i> ${video.rating}</span>
                            <span><i class="fas fa-calendar"></i> ${this.getRelativeTime(video.publishDate)}</span>
                        </div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }
};
