<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短剧视频网站</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>短剧TV</h2>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link active">首页</a>
                <a href="#" class="nav-link">热门</a>
                <a href="#" class="nav-link">分类</a>
                <a href="#" class="nav-link">收藏</a>
            </div>
            <div class="nav-search">
                <input type="text" placeholder="搜索短剧..." id="searchInput">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 轮播图区域 -->
        <section class="hero-section">
            <div class="hero-slider">
                <div class="slide active">
                    <div class="slide-content">
                        <h3>热门短剧推荐</h3>
                        <p>精彩短剧，每日更新</p>
                        <button class="play-btn">立即观看</button>
                    </div>
                    <div class="slide-bg" style="background-image: url('https://via.placeholder.com/1200x600/4a90e2/ffffff?text=短剧1')"></div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h3>经典回顾</h3>
                        <p>经典短剧重温</p>
                        <button class="play-btn">立即观看</button>
                    </div>
                    <div class="slide-bg" style="background-image: url('https://via.placeholder.com/1200x600/e74c3c/ffffff?text=短剧2')"></div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <h3>最新上线</h3>
                        <p>全新短剧首发</p>
                        <button class="play-btn">立即观看</button>
                    </div>
                    <div class="slide-bg" style="background-image: url('https://via.placeholder.com/1200x600/2ecc71/ffffff?text=短剧3')"></div>
                </div>
            </div>
            <div class="slider-controls">
                <button class="prev-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="next-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
            <div class="slider-dots">
                <span class="dot active" data-slide="0"></span>
                <span class="dot" data-slide="1"></span>
                <span class="dot" data-slide="2"></span>
            </div>
        </section>

        <!-- 分类筛选 -->
        <section class="filter-section">
            <div class="container">
                <div class="filter-tabs">
                    <button class="filter-btn active" data-category="all">全部</button>
                    <button class="filter-btn" data-category="romance">言情</button>
                    <button class="filter-btn" data-category="comedy">喜剧</button>
                    <button class="filter-btn" data-category="drama">剧情</button>
                    <button class="filter-btn" data-category="action">动作</button>
                    <button class="filter-btn" data-category="suspense">悬疑</button>
                </div>
            </div>
        </section>

        <!-- 视频列表 -->
        <section class="video-section">
            <div class="container">
                <h2 class="section-title">热门短剧</h2>
                <div class="video-grid" id="videoGrid">
                    <!-- 视频卡片将通过JavaScript动态生成 -->
                </div>
                <div class="load-more">
                    <button class="load-more-btn" id="loadMoreBtn">加载更多</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 视频播放器模态框 -->
    <div class="video-modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">视频标题</h3>
                <button class="close-btn" id="closeModal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="video-player">
                    <video id="videoPlayer" controls>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div class="video-info">
                    <p id="videoDescription">视频描述</p>
                    <div class="video-actions">
                        <button class="action-btn"><i class="fas fa-thumbs-up"></i> 点赞</button>
                        <button class="action-btn"><i class="fas fa-heart"></i> 收藏</button>
                        <button class="action-btn"><i class="fas fa-share"></i> 分享</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <p>专业的短剧视频平台，为您提供优质的短剧内容。</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>邮箱: <EMAIL></p>
                    <p>电话: 400-123-4567</p>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-wechat"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 短剧TV. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
