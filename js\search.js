// 搜索页面JavaScript

// DOM元素
const searchKeyword = document.getElementById('searchKeyword');
const resultCount = document.getElementById('resultCount');
const searchTime = document.getElementById('searchTime');
const advancedSearch = document.getElementById('advancedSearch');
const toggleAdvanced = document.getElementById('toggleAdvanced');
const keywordInput = document.getElementById('keywordInput');
const categorySelect = document.getElementById('categorySelect');
const durationSelect = document.getElementById('durationSelect');
const dateSelect = document.getElementById('dateSelect');
const advancedSearchBtn = document.getElementById('advancedSearchBtn');
const resetBtn = document.getElementById('resetBtn');
const searchSuggestions = document.getElementById('searchSuggestions');
const suggestionsList = document.getElementById('suggestionsList');
const filterTags = document.getElementById('filterTags');
const sortSelect = document.getElementById('sortSelect');
const viewBtns = document.querySelectorAll('.view-btn');
const resultsGrid = document.getElementById('resultsGrid');
const loading = document.getElementById('loading');
const noResults = document.getElementById('noResults');
const pagination = document.getElementById('pagination');
const prevPage = document.getElementById('prevPage');
const nextPage = document.getElementById('nextPage');
const pageNumbers = document.getElementById('pageNumbers');
const hotKeywords = document.getElementById('hotKeywords');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const navToggle = document.getElementById('navToggle');

// 状态变量
let currentKeyword = '';
let currentFilters = {
    category: '',
    duration: '',
    date: ''
};
let currentSort = 'relevance';
let currentView = 'grid';
let currentPage = 1;
let itemsPerPage = 12;
let searchResults = [];
let searchStartTime = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    bindEvents();
});

// 监听数据加载完成事件
document.addEventListener('dataLoaded', function() {
    initializePage();
    renderHotKeywords();
});

// 初始化页面
function initializePage() {
    // 从URL参数获取搜索关键词
    const urlParams = new URLSearchParams(window.location.search);
    const keyword = urlParams.get('q') || '';
    const category = urlParams.get('category') || '';
    
    currentKeyword = keyword;
    currentFilters.category = category;
    
    // 设置搜索框值
    searchInput.value = keyword;
    keywordInput.value = keyword;
    categorySelect.value = category;
    
    // 显示搜索关键词
    searchKeyword.textContent = keyword || '全部内容';
    
    if (keyword) {
        performSearch();
    } else {
        showAllVideos();
    }
}

// 执行搜索
function performSearch() {
    showLoading(true);
    searchStartTime = Date.now();

    // 确保数据已加载
    if (!window.utils || !videoData || videoData.length === 0) {
        setTimeout(performSearch, 100); // 等待数据加载
        return;
    }

    // 模拟搜索延迟
    setTimeout(() => {
        searchResults = utils.searchVideos(currentKeyword, currentFilters.category, currentSort);
        
        // 应用其他筛选条件
        searchResults = applyAdvancedFilters(searchResults);
        
        const searchEndTime = Date.now();
        const searchDuration = ((searchEndTime - searchStartTime) / 1000).toFixed(2);
        
        updateSearchResults(searchDuration);
        renderResults();
        renderPagination();
        renderFilterTags();
        showSearchSuggestions();
        showLoading(false);
        
        // 更新URL
        updateURL();
    }, 300);
}

// 显示所有视频
function showAllVideos() {
    showLoading(true);
    searchStartTime = Date.now();

    // 确保数据已加载
    if (!videoData || videoData.length === 0) {
        setTimeout(showAllVideos, 100); // 等待数据加载
        return;
    }

    setTimeout(() => {
        searchResults = [...videoData].sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
        
        const searchEndTime = Date.now();
        const searchDuration = ((searchEndTime - searchStartTime) / 1000).toFixed(2);
        
        updateSearchResults(searchDuration);
        renderResults();
        renderPagination();
        showLoading(false);
    }, 300);
}

// 应用高级筛选条件
function applyAdvancedFilters(results) {
    let filtered = [...results];
    
    // 时长筛选
    if (currentFilters.duration) {
        filtered = filtered.filter(video => {
            const duration = parseDuration(video.duration);
            switch (currentFilters.duration) {
                case 'short':
                    return duration < 600; // 10分钟以下
                case 'medium':
                    return duration >= 600 && duration <= 1200; // 10-20分钟
                case 'long':
                    return duration > 1200; // 20分钟以上
                default:
                    return true;
            }
        });
    }
    
    // 发布时间筛选
    if (currentFilters.date) {
        const now = new Date();
        filtered = filtered.filter(video => {
            const publishDate = new Date(video.publishDate);
            const diffTime = now - publishDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            switch (currentFilters.date) {
                case 'today':
                    return diffDays <= 1;
                case 'week':
                    return diffDays <= 7;
                case 'month':
                    return diffDays <= 30;
                case 'year':
                    return diffDays <= 365;
                default:
                    return true;
            }
        });
    }
    
    return filtered;
}

// 解析时长字符串为秒数
function parseDuration(durationStr) {
    const parts = durationStr.split(':');
    return parseInt(parts[0]) * 60 + parseInt(parts[1]);
}

// 更新搜索结果信息
function updateSearchResults(searchDuration) {
    resultCount.textContent = searchResults.length;
    searchTime.textContent = searchDuration;
    
    if (searchResults.length === 0) {
        showNoResults(true);
    } else {
        showNoResults(false);
    }
}

// 渲染搜索结果
function renderResults() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const resultsToShow = searchResults.slice(startIndex, endIndex);
    
    resultsGrid.innerHTML = '';
    resultsGrid.className = `results-grid ${currentView === 'list' ? 'list-view' : ''}`;
    
    resultsToShow.forEach(video => {
        const cardElement = document.createElement('div');
        cardElement.innerHTML = utils.createVideoCard(video, currentView === 'list', currentKeyword);
        const card = cardElement.firstElementChild;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            window.location.href = `detail.html?id=${video.id}`;
        });
        
        resultsGrid.appendChild(card);
    });
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(searchResults.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    
    // 更新上一页/下一页按钮
    prevPage.disabled = currentPage === 1;
    nextPage.disabled = currentPage === totalPages;
    
    // 生成页码
    pageNumbers.innerHTML = '';
    
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // 第一页
    if (startPage > 1) {
        pageNumbers.appendChild(createPageButton(1));
        if (startPage > 2) {
            pageNumbers.appendChild(createEllipsis());
        }
    }
    
    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        pageNumbers.appendChild(createPageButton(i));
    }
    
    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pageNumbers.appendChild(createEllipsis());
        }
        pageNumbers.appendChild(createPageButton(totalPages));
    }
}

// 创建页码按钮
function createPageButton(pageNum) {
    const button = document.createElement('button');
    button.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
    button.textContent = pageNum;
    button.addEventListener('click', () => {
        currentPage = pageNum;
        renderResults();
        renderPagination();
        scrollToTop();
    });
    return button;
}

// 创建省略号
function createEllipsis() {
    const ellipsis = document.createElement('span');
    ellipsis.className = 'page-ellipsis';
    ellipsis.textContent = '...';
    return ellipsis;
}

// 渲染筛选标签
function renderFilterTags() {
    filterTags.innerHTML = '';
    
    // 关键词标签
    if (currentKeyword) {
        const keywordTag = createFilterTag('关键词', currentKeyword, () => {
            currentKeyword = '';
            keywordInput.value = '';
            searchKeyword.textContent = '全部内容';
            performSearch();
        });
        filterTags.appendChild(keywordTag);
    }
    
    // 分类标签
    if (currentFilters.category) {
        const categoryName = utils.getCategoryName(currentFilters.category);
        const categoryTag = createFilterTag('分类', categoryName, () => {
            currentFilters.category = '';
            categorySelect.value = '';
            performSearch();
        });
        filterTags.appendChild(categoryTag);
    }
    
    // 时长标签
    if (currentFilters.duration) {
        const durationNames = {
            'short': '10分钟以下',
            'medium': '10-20分钟',
            'long': '20分钟以上'
        };
        const durationTag = createFilterTag('时长', durationNames[currentFilters.duration], () => {
            currentFilters.duration = '';
            durationSelect.value = '';
            performSearch();
        });
        filterTags.appendChild(durationTag);
    }
    
    // 发布时间标签
    if (currentFilters.date) {
        const dateNames = {
            'today': '今天',
            'week': '本周',
            'month': '本月',
            'year': '今年'
        };
        const dateTag = createFilterTag('发布时间', dateNames[currentFilters.date], () => {
            currentFilters.date = '';
            dateSelect.value = '';
            performSearch();
        });
        filterTags.appendChild(dateTag);
    }
}

// 创建筛选标签
function createFilterTag(label, value, removeCallback) {
    const tag = document.createElement('div');
    tag.className = 'filter-tag';
    tag.innerHTML = `
        ${label}: ${value}
        <button class="remove-tag" type="button">×</button>
    `;
    
    tag.querySelector('.remove-tag').addEventListener('click', removeCallback);
    return tag;
}

// 显示搜索建议
function showSearchSuggestions() {
    if (searchResults.length === 0 && currentKeyword) {
        // 生成搜索建议
        const suggestions = generateSearchSuggestions(currentKeyword);
        if (suggestions.length > 0) {
            suggestionsList.innerHTML = '';
            suggestions.forEach(suggestion => {
                const suggestionLink = document.createElement('a');
                suggestionLink.className = 'suggestion-item';
                suggestionLink.href = `search.html?q=${encodeURIComponent(suggestion)}`;
                suggestionLink.textContent = suggestion;
                suggestionsList.appendChild(suggestionLink);
            });
            searchSuggestions.style.display = 'block';
        }
    } else {
        searchSuggestions.style.display = 'none';
    }
}

// 生成搜索建议
function generateSearchSuggestions(keyword) {
    const suggestions = [];
    
    // 基于现有视频标题和标签生成建议
    videoData.forEach(video => {
        video.tags.forEach(tag => {
            if (tag.includes(keyword) && !suggestions.includes(tag)) {
                suggestions.push(tag);
            }
        });
    });
    
    // 添加一些通用建议
    const commonSuggestions = ['言情', '喜剧', '悬疑', '动作', '校园', '都市'];
    commonSuggestions.forEach(suggestion => {
        if (suggestion.includes(keyword) && !suggestions.includes(suggestion)) {
            suggestions.push(suggestion);
        }
    });
    
    return suggestions.slice(0, 6);
}

// 渲染热门关键词
function renderHotKeywords() {
    if (!window.hotKeywords || !hotKeywords) {
        return;
    }

    hotKeywords.innerHTML = '';

    window.hotKeywords.forEach(item => {
        const keywordLink = document.createElement('a');
        keywordLink.className = `hot-keyword ${item.trending ? 'trending' : ''}`;
        keywordLink.href = `search.html?q=${encodeURIComponent(item.keyword)}`;
        keywordLink.textContent = item.keyword;
        hotKeywords.appendChild(keywordLink);
    });
}

// 显示/隐藏加载状态
function showLoading(show) {
    loading.style.display = show ? 'block' : 'none';
    resultsGrid.style.display = show ? 'none' : 'grid';
}

// 显示/隐藏无结果状态
function showNoResults(show) {
    noResults.style.display = show ? 'block' : 'none';
    resultsGrid.style.display = show ? 'none' : 'grid';
    pagination.style.display = show ? 'none' : 'flex';
}

// 更新URL
function updateURL() {
    const url = new URL(window.location);
    
    if (currentKeyword) {
        url.searchParams.set('q', currentKeyword);
    } else {
        url.searchParams.delete('q');
    }
    
    if (currentFilters.category) {
        url.searchParams.set('category', currentFilters.category);
    } else {
        url.searchParams.delete('category');
    }
    
    window.history.pushState({}, '', url);
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 绑定事件
function bindEvents() {
    // 高级搜索切换
    toggleAdvanced.addEventListener('click', () => {
        const isShown = advancedSearch.classList.contains('show');
        advancedSearch.classList.toggle('show', !isShown);
        toggleAdvanced.innerHTML = `<i class="fas fa-sliders-h"></i> ${isShown ? '高级搜索' : '收起搜索'}`;
    });
    
    // 高级搜索
    advancedSearchBtn.addEventListener('click', () => {
        currentKeyword = keywordInput.value.trim();
        currentFilters.category = categorySelect.value;
        currentFilters.duration = durationSelect.value;
        currentFilters.date = dateSelect.value;
        currentPage = 1;
        
        searchKeyword.textContent = currentKeyword || '全部内容';
        searchInput.value = currentKeyword;
        
        performSearch();
    });
    
    // 重置搜索
    resetBtn.addEventListener('click', () => {
        keywordInput.value = '';
        categorySelect.value = '';
        durationSelect.value = '';
        dateSelect.value = '';
        
        currentKeyword = '';
        currentFilters = { category: '', duration: '', date: '' };
        currentPage = 1;
        
        searchKeyword.textContent = '全部内容';
        searchInput.value = '';
        
        showAllVideos();
    });
    
    // 排序
    sortSelect.addEventListener('change', () => {
        currentSort = sortSelect.value;
        currentPage = 1;
        performSearch();
    });
    
    // 视图切换
    viewBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            viewBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentView = btn.dataset.view;
            renderResults();
        });
    });
    
    // 搜索
    searchBtn.addEventListener('click', performMainSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performMainSearch();
        }
    });
    
    // 分页
    prevPage.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderResults();
            renderPagination();
            scrollToTop();
        }
    });
    
    nextPage.addEventListener('click', () => {
        const totalPages = Math.ceil(searchResults.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderResults();
            renderPagination();
            scrollToTop();
        }
    });
    
    // 移动端导航
    navToggle.addEventListener('click', () => {
        const navMenu = document.querySelector('.nav-menu');
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });
}

// 执行主搜索
function performMainSearch() {
    const keyword = searchInput.value.trim();
    if (keyword !== currentKeyword) {
        window.location.href = `search.html?q=${encodeURIComponent(keyword)}`;
    }
}
