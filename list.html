<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类列表 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/list.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="#" class="nav-link">热门</a>
                <a href="#" class="nav-link active">分类</a>
                <a href="#" class="nav-link">收藏</a>
            </div>
            <div class="nav-search">
                <input type="text" placeholder="搜索短剧..." id="searchInput">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
            </div>
            <div class="nav-user" id="navUser">
                <!-- 用户信息将通过JavaScript动态生成 -->
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 面包屑导航 -->
        <section class="breadcrumb-section">
            <div class="container">
                <nav class="breadcrumb">
                    <a href="index.html"><i class="fas fa-home"></i> 首页</a>
                    <span class="separator">/</span>
                    <span class="current" id="currentCategory">分类</span>
                </nav>
            </div>
        </section>

        <!-- 页面标题和统计 -->
        <section class="page-header">
            <div class="container">
                <div class="header-content">
                    <h1 class="page-title" id="pageTitle">全部短剧</h1>
                    <p class="page-subtitle" id="pageSubtitle">发现更多精彩内容</p>
                    <div class="stats">
                        <span class="total-count" id="totalCount">共 0 部短剧</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选和排序 -->
        <section class="filter-section">
            <div class="container">
                <div class="filter-controls">
                    <div class="filter-group">
                        <label>分类:</label>
                        <div class="filter-tabs">
                            <button class="filter-btn active" data-category="all">全部</button>
                            <button class="filter-btn" data-category="romance">言情</button>
                            <button class="filter-btn" data-category="comedy">喜剧</button>
                            <button class="filter-btn" data-category="drama">剧情</button>
                            <button class="filter-btn" data-category="action">动作</button>
                            <button class="filter-btn" data-category="suspense">悬疑</button>
                        </div>
                    </div>
                    <div class="sort-group">
                        <label>排序:</label>
                        <select id="sortSelect" class="sort-select">
                            <option value="latest">最新发布</option>
                            <option value="popular">最受欢迎</option>
                            <option value="views">播放量</option>
                            <option value="rating">评分</option>
                        </select>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid" title="网格视图">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list" title="列表视图">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 视频列表 -->
        <section class="video-section">
            <div class="container">
                <div class="video-grid" id="videoGrid">
                    <!-- 视频卡片将通过JavaScript动态生成 -->
                </div>
                
                <!-- 加载状态 -->
                <div class="loading" id="loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
                
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3>暂无内容</h3>
                    <p>该分类下暂时没有短剧，请尝试其他分类</p>
                    <button class="back-btn" onclick="window.location.href='index.html'">返回首页</button>
                </div>
                
                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <button class="page-btn prev-page" id="prevPage" disabled>
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <div class="page-numbers" id="pageNumbers">
                        <!-- 页码将通过JavaScript生成 -->
                    </div>
                    <button class="page-btn next-page" id="nextPage">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <p>专业的短剧视频平台，为您提供优质的短剧内容。</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>邮箱: <EMAIL></p>
                    <p>电话: 400-123-4567</p>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-wechat"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 短剧TV. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script src="js/dataLoader.js"></script>
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/list.js"></script>
</body>
</html>
