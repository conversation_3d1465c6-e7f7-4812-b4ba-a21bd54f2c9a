<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 短剧TV</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html"><h2>短剧TV</h2></a>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">首页</a>
                <a href="list.html?category=all" class="nav-link">热门</a>
                <a href="list.html" class="nav-link">分类</a>
                <a href="search.html" class="nav-link">搜索</a>
            </div>
            <div class="nav-user" id="navUser">
                <!-- 用户信息将通过JavaScript动态生成 -->
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 用户信息头部 -->
        <section class="profile-header">
            <div class="container">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <img id="userAvatar" src="https://via.placeholder.com/120x120/667eea/ffffff?text=用户" alt="用户头像">
                        <button class="avatar-edit" id="editAvatar">
                            <i class="fas fa-camera"></i>
                        </button>
                    </div>
                    <div class="profile-details">
                        <h1 class="username" id="username">用户名</h1>
                        <p class="user-email" id="userEmail"><EMAIL></p>
                        <div class="user-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="watchedCount">0</span>
                                <span class="stat-label">已观看</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="favoriteCount">0</span>
                                <span class="stat-label">收藏</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="joinDays">0</span>
                                <span class="stat-label">加入天数</span>
                            </div>
                        </div>
                        <button class="edit-profile-btn" id="editProfileBtn">
                            <i class="fas fa-edit"></i> 编辑资料
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 导航标签 -->
        <section class="profile-nav">
            <div class="container">
                <div class="nav-tabs">
                    <button class="tab-btn active" data-tab="history">
                        <i class="fas fa-history"></i> 观看历史
                    </button>
                    <button class="tab-btn" data-tab="favorites">
                        <i class="fas fa-heart"></i> 我的收藏
                    </button>
                    <button class="tab-btn" data-tab="settings">
                        <i class="fas fa-cog"></i> 账户设置
                    </button>
                </div>
            </div>
        </section>

        <!-- 内容区域 -->
        <section class="profile-content">
            <div class="container">
                <!-- 观看历史 -->
                <div class="tab-content active" id="historyTab">
                    <div class="content-header">
                        <h2>观看历史</h2>
                        <div class="content-actions">
                            <button class="action-btn" id="clearHistoryBtn">
                                <i class="fas fa-trash"></i> 清空历史
                            </button>
                        </div>
                    </div>
                    <div class="history-list" id="historyList">
                        <!-- 历史记录将通过JavaScript生成 -->
                    </div>
                    <div class="empty-state" id="historyEmpty" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <h3>暂无观看历史</h3>
                        <p>开始观看视频，这里会显示您的观看记录</p>
                        <a href="index.html" class="empty-action">去首页看看</a>
                    </div>
                </div>

                <!-- 我的收藏 -->
                <div class="tab-content" id="favoritesTab">
                    <div class="content-header">
                        <h2>我的收藏</h2>
                        <div class="content-actions">
                            <select class="sort-select" id="favoritesSort">
                                <option value="latest">最近收藏</option>
                                <option value="oldest">最早收藏</option>
                                <option value="name">按名称</option>
                            </select>
                        </div>
                    </div>
                    <div class="favorites-grid" id="favoritesList">
                        <!-- 收藏列表将通过JavaScript生成 -->
                    </div>
                    <div class="empty-state" id="favoritesEmpty" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>暂无收藏内容</h3>
                        <p>收藏您喜欢的视频，方便随时观看</p>
                        <a href="index.html" class="empty-action">去发现内容</a>
                    </div>
                </div>

                <!-- 账户设置 -->
                <div class="tab-content" id="settingsTab">
                    <div class="settings-section">
                        <h3>个人信息</h3>
                        <form class="settings-form" id="profileForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="settingsUsername">用户名</label>
                                    <input type="text" id="settingsUsername" name="username" placeholder="请输入用户名">
                                </div>
                                <div class="form-group">
                                    <label for="settingsEmail">邮箱地址</label>
                                    <input type="email" id="settingsEmail" name="email" placeholder="请输入邮箱地址">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="settingsPhone">手机号码</label>
                                    <input type="tel" id="settingsPhone" name="phone" placeholder="请输入手机号码">
                                </div>
                                <div class="form-group">
                                    <label for="settingsBirthday">生日</label>
                                    <input type="date" id="settingsBirthday" name="birthday">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="settingsBio">个人简介</label>
                                <textarea id="settingsBio" name="bio" placeholder="介绍一下自己吧..." rows="3"></textarea>
                            </div>
                            <button type="submit" class="save-btn">
                                <i class="fas fa-save"></i> 保存更改
                            </button>
                        </form>
                    </div>

                    <div class="settings-section">
                        <h3>隐私设置</h3>
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>观看历史</h4>
                                <p>是否记录您的观看历史</p>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="recordHistory" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>个性化推荐</h4>
                                <p>基于您的观看历史推荐内容</p>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="personalizedRecommend" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3>账户安全</h3>
                        <div class="security-actions">
                            <button class="security-btn" id="changePasswordBtn">
                                <i class="fas fa-key"></i> 修改密码
                            </button>
                            <button class="security-btn danger" id="deleteAccountBtn">
                                <i class="fas fa-user-times"></i> 删除账户
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 编辑资料模态框 -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑个人资料</h3>
                <button class="close-btn" id="closeEditModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm">
                    <div class="form-group">
                        <label for="editUsername">用户名</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">邮箱地址</label>
                        <input type="email" id="editEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editPhone">手机号码</label>
                        <input type="tel" id="editPhone" name="phone">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" id="cancelEdit">取消</button>
                        <button type="submit" class="save-btn">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <p>专业的短剧视频平台，为您提供优质的短剧内容。</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>邮箱: <EMAIL></p>
                    <p>电话: ************</p>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-wechat"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 短剧TV. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/profile.js"></script>
</body>
</html>
