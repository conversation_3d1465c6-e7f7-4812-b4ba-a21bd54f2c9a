/* 详情页面专用样式 */

/* 视频播放器区域 */
.video-player-section {
    background: #000;
    padding: 0;
}

.player-wrapper {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.video-player {
    position: relative;
    width: 100%;
    background: #000;
}

.video-player video {
    width: 100%;
    height: auto;
    max-height: 70vh;
    display: block;
}

.player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.player-overlay.show {
    opacity: 1;
    pointer-events: auto;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #333;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

/* 视频信息区域 */
.video-info-section {
    padding: 2rem 0;
    background: white;
}

.video-details {
    display: grid;
    gap: 2rem;
}

.video-main-info {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 2rem;
}

.video-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.video-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.95rem;
}

.meta-item i {
    color: #667eea;
}

.category-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.video-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e9ecef;
    background: white;
    color: #495057;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.action-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102,126,234,0.4);
}

.action-btn .count {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
}

/* 视频描述 */
.video-description {
    padding: 1.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.video-description h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.description-content {
    color: #495057;
    line-height: 1.7;
    max-height: 120px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.description-content.expanded {
    max-height: none;
}

.expand-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    margin-top: 1rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.expand-btn:hover {
    color: #764ba2;
}

/* 标签 */
.video-tags {
    padding: 1.5rem 0;
}

.video-tags h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.tag {
    background: #f8f9fa;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

/* 评论区域 */
.comments-section {
    background: #f8f9fa;
    padding: 3rem 0;
}

.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.comments-header h3 {
    font-size: 1.5rem;
    color: #333;
}

.comment-sort select {
    padding: 0.5rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    background: white;
    color: #495057;
    cursor: pointer;
}

.comment-form {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.comment-input textarea {
    width: 100%;
    min-height: 100px;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    resize: vertical;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
}

.comment-input textarea:focus {
    outline: none;
    border-color: #667eea;
}

.comment-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.comment-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.comment-info {
    flex: 1;
}

.comment-author {
    font-weight: 500;
    color: #333;
}

.comment-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.comment-content {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.comment-actions-bar {
    display: flex;
    gap: 1rem;
}

.comment-action {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.comment-action:hover {
    color: #667eea;
}

.load-more-comments {
    text-align: center;
    margin-top: 2rem;
}

/* 相关推荐 */
.related-section {
    padding: 3rem 0;
    background: white;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* 分享模态框 */
.share-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.share-modal.show {
    display: flex;
}

.share-modal .modal-content {
    background: white;
    border-radius: 15px;
    padding: 0;
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.share-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 15px 15px 0 0;
}

.share-modal .modal-body {
    padding: 2rem;
}

.share-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.share-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.share-option:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.share-option i {
    font-size: 2rem;
    color: #667eea;
}

.share-url {
    display: flex;
    gap: 0.5rem;
}

.share-url input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
}

.copy-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .video-title {
        font-size: 1.5rem;
    }
    
    .video-meta {
        gap: 1rem;
    }
    
    .video-actions {
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .comments-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .related-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .share-options {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .video-info-section {
        padding: 1rem 0;
    }
    
    .video-title {
        font-size: 1.3rem;
    }
    
    .video-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .video-actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
    
    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .related-grid {
        grid-template-columns: 1fr;
    }
    
    .share-options {
        grid-template-columns: 1fr;
    }
}
