/* 认证页面样式 */

.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.auth-main {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
    margin-top: 80px;
}

.auth-container {
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    padding: 0 20px;
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-header h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.auth-header p {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.5;
}

/* 表单样式 */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.95rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 1rem;
    color: #6c757d;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.input-wrapper input::placeholder {
    color: #adb5bd;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #667eea;
}

/* 字段提示 */
.field-hint {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 密码强度指示器 */
.password-strength {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    background: #dc3545;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 33%;
    background: #dc3545;
}

.strength-fill.medium {
    width: 66%;
    background: #ffc107;
}

.strength-fill.strong {
    width: 100%;
    background: #28a745;
}

.strength-text {
    font-size: 0.8rem;
    color: #6c757d;
    white-space: nowrap;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #495057;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #764ba2;
}

.terms-link {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: #764ba2;
}

/* 按钮样式 */
.auth-btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.auth-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102,126,234,0.3);
}

.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-loading {
    display: none;
}

.auth-btn.loading .btn-text {
    display: none;
}

.auth-btn.loading .btn-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 分割线 */
.auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    color: #6c757d;
    padding: 0 1rem;
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* 社交登录 */
.social-login {
    display: flex;
    gap: 1rem;
}

.social-btn {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.social-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

.social-btn.wechat:hover {
    border-color: #07c160;
    color: #07c160;
}

.social-btn.qq:hover {
    border-color: #12b7f5;
    color: #12b7f5;
}

/* 页脚链接 */
.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.auth-footer p {
    color: #6c757d;
    font-size: 0.95rem;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #764ba2;
}

/* 背景装饰 */
.auth-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-item {
    position: absolute;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.decoration-item.item-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.decoration-item.item-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.decoration-item.item-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 9999;
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.success .toast-icon {
    color: #28a745;
}

.toast.error .toast-icon {
    color: #dc3545;
}

.toast.warning .toast-icon {
    color: #ffc107;
}

.toast-message {
    color: #333;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-main {
        padding: 1rem 0;
    }
    
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 0 10px;
        border-radius: 15px;
    }
    
    .auth-header h1 {
        font-size: 1.5rem;
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .input-wrapper input {
        padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        font-size: 0.95rem;
    }
    
    .input-wrapper i {
        left: 0.875rem;
    }
    
    .toggle-password {
        right: 0.875rem;
    }
}
